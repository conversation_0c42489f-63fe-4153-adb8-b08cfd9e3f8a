{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "c522c70d8958b729279df4ecda413474", "files": {"compare_timestamps_py": {"hash": "1a9e1bfcd10a2b8949cedbaa5884fac2", "index": {"url": "compare_timestamps_py.html", "file": "compare_timestamps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 2, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_consolidated_webodm_poses_py": {"hash": "25d06b2aae4bab0a660481c123145ea7", "index": {"url": "create_consolidated_webodm_poses_py.html", "file": "create_consolidated_webodm_poses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 17, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_georeferenced_rgb_py": {"hash": "868d7a901234324017367000108d799f", "index": {"url": "create_georeferenced_rgb_py.html", "file": "create_georeferenced_rgb.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 12, "n_missing": 178, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "georeference_hsi_pixels_py": {"hash": "c11497b8273246d9029933755ac56129", "index": {"url": "georeference_hsi_pixels_py.html", "file": "georeference_hsi_pixels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 418, "n_excluded": 17, "n_missing": 398, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "lever_arm_utils_py": {"hash": "994a2bd316f52c289200a79e06196842", "index": {"url": "lever_arm_utils_py.html", "file": "lever_arm_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "logging_config_py": {"hash": "0feb55ecfbd80cf285598bde5e434f46", "index": {"url": "logging_config_py.html", "file": "logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_pipeline_py": {"hash": "c5ac4832a0f42449701718fa42356d1c", "index": {"url": "main_pipeline_py.html", "file": "main_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 6, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pipeline_exceptions_py": {"hash": "c0b3ee11f3c2400581dd40465b355f4e", "index": {"url": "pipeline_exceptions_py.html", "file": "pipeline_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "plot_hsi_data_py": {"hash": "5e53c967da82b8cd45d6382ca798205e", "index": {"url": "plot_hsi_data_py.html", "file": "plot_hsi_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 8, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "synchronize_hsi_webodm_py": {"hash": "eee60b1e938019b7c5648a262ad17b2b", "index": {"url": "synchronize_hsi_webodm_py.html", "file": "synchronize_hsi_webodm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 17, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "vectorized_georef_py": {"hash": "efb4dcdb8dfcca86f0e00310ce3b5eac", "index": {"url": "vectorized_georef_py.html", "file": "vectorized_georef.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}