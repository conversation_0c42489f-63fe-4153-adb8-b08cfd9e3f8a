{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "c522c70d8958b729279df4ecda413474", "files": {"compare_timestamps_py": {"hash": "1a9e1bfcd10a2b8949cedbaa5884fac2", "index": {"url": "compare_timestamps_py.html", "file": "compare_timestamps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 2, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_consolidated_webodm_poses_py": {"hash": "d4b0d65929ed61ca9e6b94c1feeec38c", "index": {"url": "create_consolidated_webodm_poses_py.html", "file": "create_consolidated_webodm_poses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 17, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_georeferenced_rgb_py": {"hash": "7aa6a4222e82201ad5ff5a69e7ce2ef5", "index": {"url": "create_georeferenced_rgb_py.html", "file": "create_georeferenced_rgb.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 12, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "georeference_hsi_pixels_py": {"hash": "4e2897605cf1d888e519f90b248ca3b2", "index": {"url": "georeference_hsi_pixels_py.html", "file": "georeference_hsi_pixels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 417, "n_excluded": 17, "n_missing": 397, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "lever_arm_utils_py": {"hash": "6d270b2a1654d1a41107ede2d7bb4f97", "index": {"url": "lever_arm_utils_py.html", "file": "lever_arm_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "logging_config_py": {"hash": "d7ab2c5766f2ab53e4839a091ed9443d", "index": {"url": "logging_config_py.html", "file": "logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_pipeline_py": {"hash": "9ab80a2838bf44513e2e95d40b490ac9", "index": {"url": "main_pipeline_py.html", "file": "main_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 6, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pipeline_exceptions_py": {"hash": "c0b3ee11f3c2400581dd40465b355f4e", "index": {"url": "pipeline_exceptions_py.html", "file": "pipeline_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "plot_hsi_data_py": {"hash": "be244aaa44e9420caf7ad5d78965478d", "index": {"url": "plot_hsi_data_py.html", "file": "plot_hsi_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 8, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "synchronize_hsi_webodm_py": {"hash": "e3423a6faec62d605ac3f63c69cd47f6", "index": {"url": "synchronize_hsi_webodm_py.html", "file": "synchronize_hsi_webodm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 17, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "vectorized_georef_py": {"hash": "f2a238b29ab637b5ec0f59e747c1ea9e", "index": {"url": "vectorized_georef_py.html", "file": "vectorized_georef.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}