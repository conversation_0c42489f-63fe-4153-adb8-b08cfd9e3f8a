# Test Specifications and Scaffolding for Layer LS4

## Introduction

This document outlines the test specifications and scaffolding for Layer LS4, based on the refined prompts in [`prompts_LS4.md`](prompts_LS4.md) and the issues/feedback from [`reflection_LS3.md`](reflection_LS3.md) and [`scores_LS3.json`](scores_LS3.json). The goal is to address existing test failures, improve test coverage, support refactoring efforts, and verify documentation/style enhancements.

## 1. Addressing Test Failures (Prompt [LS4_1])

This section details test cases to resolve the three outstanding test failures identified in [`reflection_LS3.md`](reflection_LS3.md).

### 1.1 Test Case: `test_process_hsi_line_invalid_quaternion_raises_error`
   - **Source Issue**: [`reflection_LS3.md`](reflection_LS3.md) (Issue 1) - `test_process_hsi_line_invalid_quaternion` in [`test_vectorized_georef.py`](test_vectorized_georef.py:255-281)
   - **Objective**: Verify that `process_hsi_line_vectorized` correctly raises a `PoseTransformationError` when an invalid quaternion is provided, instead of returning NaNs.
   - **File to Modify**: [`test_vectorized_georef.py`](test_vectorized_georef.py)
   - **Inputs**:
     - `line_index`: A valid integer (e.g., 0).
     - `pose_data`: A dictionary or structured array containing an invalid quaternion (e.g., `[0, 0, 0, 0]`).
     - `num_samples`: Integer, number of pixels (e.g., 10).
     - `vinkelx_rad_all`, `vinkely_rad_all`: Numpy arrays of angles.
     - `R_sensor_to_body`: 3x3 Numpy array (identity or valid rotation).
     - `effective_lever_arm_body`: 3-element Numpy array.
   - **Action**: Call `process_hsi_line_vectorized` with the prepared inputs.
   - **Expected Output/Behavior**: A `PoseTransformationError` is raised, ideally with a message indicating an invalid quaternion.
   - **Acceptance Criteria**:
     - The test passes if `pytest.raises(PoseTransformationError)` successfully catches the exception.
     - The exception message should match a predefined pattern (e.g., "Invalid quaternion").
   - **Test Scaffolding (Python/pytest)**:
     ```python
     import pytest
     import numpy as np
     # from ..vectorized_georef import process_hsi_line_vectorized # Adjust import path
     # from ..pipeline_exceptions import PoseTransformationError # Adjust import path

     def test_process_hsi_line_invalid_quaternion_raises_error(self):
         # Arrange
         line_index = 0
         num_samples = 5
         # Example: Invalid quaternion (not normalized, or all zeros)
         # Ensure this matches how pose_data is structured and accessed in the function
         pose_data = { 
             'qw': 0.0, 'qx': 0.0, 'qy': 0.0, 'qz': 0.0, 
             'X': 10.0, 'Y': 20.0, 'Z': 30.0 
         } 
         # Or if it's a direct array:
         # pose_data_array = np.array([(0, 0, 0, 0, 10, 20, 30)], 
         # dtype=[('qw', 'f8'), ('qx', 'f8'), ('qy', 'f8'), ('qz', 'f8'), 
         #        ('X', 'f8'), ('Y', 'f8'), ('Z', 'f8')]) 
         # pose_data = pose_data_array[0] # if the function expects a single record

         vinkelx_rad_all = np.random.rand(num_samples)
         vinkely_rad_all = np.random.rand(num_samples)
         R_sensor_to_body = np.eye(3)
         effective_lever_arm_body = np.array([0.1, 0.05, -0.02])
         
         # Placeholder for actual function and exception
         # from georeference_hsi_pixels import process_hsi_line_vectorized # Assuming location
         from pipeline_exceptions import PoseTransformationError

         # Act & Assert
         with pytest.raises(PoseTransformationError, match="Invalid quaternion"):
             process_hsi_line_vectorized(
                 line_index, pose_data, num_samples, vinkelx_rad_all, vinkely_rad_all,
                 R_sensor_to_body, effective_lever_arm_body
             )
     ```

### 1.2 Test Case: `test_run_georeferencing_fallback_on_pose_transformation_error`
   - **Source Issue**: [`reflection_LS3.md`](reflection_LS3.md) (Issue 2) - Refactor/replace `test_invalid_quaternion_handling_in_vectorized_function` in [`test_georeferencing.py`](test_georeferencing.py:624-643).
   - **Objective**: Verify that `run_georeferencing` correctly falls back to individual pixel processing and logs a warning when `process_hsi_line_vectorized` (its internal call for flat plane mode) raises a `PoseTransformationError`.
   - **File to Modify/Create**: [`test_georeferencing.py`](test_georeferencing.py)
   - **Inputs**:
     - `config`: A configuration dictionary that enables vectorized processing (e.g., `{"processing_mode": "flat_plane", "dsm_path": null, ...other_necessary_configs...}`).
     - Mock for `georeference_hsi_pixels.process_hsi_line_vectorized` that is set to `side_effect=PoseTransformationError("Simulated invalid quaternion")`.
     - Mock for `georeference_hsi_pixels.process_hsi_line_individual` to verify it's called during fallback.
     - Mocks for file I/O, header parsing, sensor model parsing, and logging as needed to isolate `run_georeferencing`.
   - **Action**: Call `run_georeferencing(config)`.
   - **Expected Output/Behavior**:
     - `run_georeferencing` completes and returns `True` (indicating successful completion despite the error, due to fallback).
     - The mocked `process_hsi_line_vectorized` is called once.
     - The mocked `process_hsi_line_individual` is called (e.g., `num_lines * num_pixels_per_line` times, or at least once).
     - A warning message is logged indicating the fallback.
   - **Acceptance Criteria**:
     - Test passes.
     - `mock_process_vectorized.assert_called_once()` passes.
     - `mock_process_individual.called` is True (or `assert_called()`).
     - The logger's warning method is called with a message containing "Falling back to individual pixel processing" or similar.
   - **Test Scaffolding (Python/pytest)**:
     ```python
     import pytest
     from unittest.mock import patch, MagicMock
     import pandas as pd
     import numpy as np
     # from georeference_hsi_pixels import run_georeferencing # Adjust import
     # from pipeline_exceptions import PoseTransformationError # Adjust import

     @patch('georeference_hsi_pixels.process_hsi_line_vectorized')
     @patch('georeference_hsi_pixels.process_hsi_line_individual') # To check fallback
     @patch('georeference_hsi_pixels.os.makedirs')
     @patch('georeference_hsi_pixels.parse_hsi_header')
     @patch('georeference_hsi_pixels.parse_sensor_model')
     @patch('georeference_hsi_pixels.pd.read_csv')
     @patch('georeference_hsi_pixels.pd.DataFrame.to_csv')
     @patch('georeference_hsi_pixels.get_logger')
     def test_run_georeferencing_fallback_on_pose_transformation_error(
         self, mock_get_logger, mock_to_csv, mock_pd_read_csv, 
         mock_parse_sensor_model, mock_parse_hsi_header, mock_makedirs,
         mock_process_individual, mock_process_vectorized
     ):
         # Arrange
         from pipeline_exceptions import PoseTransformationError # Actual import
         from georeference_hsi_pixels import run_georeferencing # Actual import

         mock_process_vectorized.side_effect = PoseTransformationError("Simulated invalid quaternion")
         
         # Mock logger
         mock_logger_instance = MagicMock()
         mock_get_logger.return_value = mock_logger_instance

         # Mock return values for dependencies
         num_lines = 2
         samples_per_line = 3
         mock_parse_hsi_header.return_value = (samples_per_line, num_lines, np.array([0.0, 0.0, 0.0])) # samples, lines, wavelengths
         mock_parse_sensor_model.return_value = (np.zeros(samples_per_line), np.zeros(samples_per_line)) # vinkelx, vinkely
         
         # Simplified pose data for 2 lines
         poses_data_df = pd.DataFrame({
             'timestamp': [1.0, 2.0], 
             'X': [10, 11], 'Y': [20, 21], 'Z': [30, 31],
             'qw': [1, 1], 'qx': [0, 0], 'qy': [0, 0], 'qz': [0, 0] 
         })
         mock_pd_read_csv.return_value = poses_data_df
         
         # Mock process_hsi_line_individual to return some valid-looking data
         # to allow the pipeline to complete.
         # Each call processes one pixel; it will be called num_lines * samples_per_line times.
         mock_process_individual.return_value = (1.0, 2.0, 3.0)


         config = {
             "hsi_file_path": "dummy.hdr",
             "poses_file_path": "dummy_poses.csv",
             "output_path": "dummy_output.csv",
             "processing_mode": "flat_plane", # To trigger vectorized path first
             "dsm_path": None,
             "flight_altitude_m": 100,
             "sensor_angle_file": "dummy_sensor.csv",
             "lever_arm_x": 0, "lever_arm_y": 0, "lever_arm_z": 0,
             "boresight_roll": 0, "boresight_pitch": 0, "boresight_yaw": 0,
             "log_level": "INFO",
             "vectorized": True # Ensure vectorized path is attempted
         }

         # Act
         result = run_georeferencing(config)

         # Assert
         assert result is True # Pipeline should complete due to fallback
         mock_process_vectorized.assert_called() # Should be called at least once per line before error
         mock_process_individual.assert_called() # Fallback processing should occur
         
         # Check that a warning about fallback was logged
         fallback_log_found = False
         for call_args in mock_logger_instance.warning.call_args_list:
             if "Falling back to individual pixel processing" in call_args[0][0] and \
                "Simulated invalid quaternion" in call_args[0][0]:
                 fallback_log_found = True
                 break
         assert fallback_log_found, "Fallback warning not logged as expected"
     ```

### 1.3 Test Case: `log_vectorized_vs_iterative_performance`
   - **Source Issue**: [`reflection_LS3.md`](reflection_LS3.md) (Issue 3) - `test_vectorized_vs_iterative_performance` in [`test_vectorized_georef.py`](test_vectorized_georef.py:287-332).
   - **Objective**: Modify the existing performance test to log performance metrics (vectorized time, iterative time, speedup) instead of using a strict, potentially flaky assertion for the speedup ratio.
   - **File to Modify**: [`test_vectorized_georef.py`](test_vectorized_georef.py)
   - **Inputs**: Same inputs as the original `test_vectorized_vs_iterative_performance` (sample data, configurations).
   - **Action**:
     - Run the vectorized processing method and measure its execution time (`vectorized_time`).
     - Run the iterative processing method (or a comparable per-pixel processing loop) and measure its execution time (`iterative_time`).
     - Calculate `speedup = iterative_time / vectorized_time`.
   - **Expected Output/Behavior**:
     - The test completes without assertion failures related to the performance ratio.
     - `vectorized_time`, `iterative_time`, and `speedup` are logged using the `logger.info()` or `print()`.
   - **Acceptance Criteria**:
     - The test runs to completion.
     - Log output (captured by pytest or viewed manually) contains the performance metrics.
     - The original strict assertion (`assert vectorized_time <= iterative_time * N`) is removed or commented out.
   - **Test Scaffolding (Python/pytest) - Modification of existing test**:
     ```python
     import time
     import logging
     import numpy as np
     # from ..vectorized_georef import process_hsi_line_vectorized, process_hsi_line_iterative_placeholder 
     # Assuming process_hsi_line_iterative_placeholder is the iterative counterpart or a similar setup

     logger = logging.getLogger(__name__)

     def test_log_vectorized_vs_iterative_performance(self, setup_performance_test_data): # Assuming a fixture
         # Arrange
         # (Assuming setup_performance_test_data provides:
         #  line_index, pose_data, num_pixels, vinkelx_rad_all, vinkely_rad_all,
         #  R_sensor_to_body, effective_lever_arm_body,
         #  iterative_processing_function, vectorized_processing_function)
         
         (line_index, pose_data, num_pixels, vinkelx_rad_all, vinkely_rad_all,
          R_sensor_to_body, effective_lever_arm_body,
          iterative_func, vectorized_func) = setup_performance_test_data
         
         # Act: Vectorized
         start_time = time.perf_counter()
         # Replace with actual call to vectorized version
         # Example:
         # vectorized_results = vectorized_func(
         #     line_index, pose_data, num_pixels, vinkelx_rad_all, vinkely_rad_all,
         #     R_sensor_to_body, effective_lever_arm_body
         # )
         # For the purpose of this spec, let's assume it's called
         # process_hsi_line_vectorized from the module.
         # from vectorized_georef import process_hsi_line_vectorized
         # vectorized_results = process_hsi_line_vectorized(...) # Fill with params
         vectorized_time = time.perf_counter() - start_time

         # Act: Iterative
         start_time = time.perf_counter()
         # Replace with actual call to iterative version or loop
         # Example:
         # iterative_results = []
         # for i in range(num_pixels):
         #     # Simplified call to an iterative pixel processor
         #     res = iterative_func(pose_data, vinkelx_rad_all[i], vinkely_rad_all[i], R_sensor_to_body, effective_lever_arm_body)
         #     iterative_results.append(res)
         # For the purpose of this spec, let's assume it's called
         # process_hsi_pixel_iterative from the module.
         # from georeference_hsi_pixels import process_hsi_pixel_iterative # A placeholder name
         # iterative_results = [process_hsi_pixel_iterative(...) for _ in range(num_pixels)] # Fill with params
         iterative_time = time.perf_counter() - start_time

         # Assert & Log
         speedup = iterative_time / vectorized_time if vectorized_time > 0 else float('inf')
         
         log_message = (
             f"Performance Metrics for {num_pixels} pixels:\n"
             f"  Iterative Time: {iterative_time:.6f}s\n"
             f"  Vectorized Time: {vectorized_time:.6f}s\n"
             f"  Speedup (Iterative/Vectorized): {speedup:.2f}x"
         )
         logger.info(log_message)
         print(log_message) # Also print for easy visibility during test runs

         # Original assertion removed/commented:
         # assert vectorized_time <= iterative_time * 2 
         
         # Optional: A very loose assertion to catch catastrophic regressions,
         # but the primary goal is logging.
         assert vectorized_time < iterative_time * 100, "Vectorized version is drastically slower."
     ```

## 2. Improving Test Coverage (Prompt [LS4_2])

This section outlines test cases to increase overall test coverage to at least 75-80%. Focus areas include [`lever_arm_utils.py`](lever_arm_utils.py), [`main_pipeline.py`](main_pipeline.py), and further edge cases in georeferencing logic.

### 2.1 Test Cases for `lever_arm_utils.py`

#### 2.1.1 Test Case: `test_calculate_effective_lever_arm_zero_boresight`
   - **Objective**: Verify `calculate_effective_lever_arm` with zero boresight angles.
   - **File to Create/Modify**: [`test_lever_arm.py`](test_lever_arm.py)
   - **Inputs**:
     - `lever_arm_components`: `(lx, ly, lz)` e.g., `(0.1, 0.05, -0.02)`
     - `boresight_angles_rad`: `(br, bp, by)` e.g., `(0.0, 0.0, 0.0)`
   - **Action**: Call `calculate_effective_lever_arm(lever_arm_components, boresight_angles_rad)`.
   - **Expected Output/Behavior**: The effective lever arm should be identical to the input `lever_arm_components`.
   - **Acceptance Criteria**: `np.array_equal(result, np.array(lever_arm_components))` is true.
   - **Test Scaffolding**:
     ```python
     import numpy as np
     # from lever_arm_utils import calculate_effective_lever_arm

     def test_calculate_effective_lever_arm_zero_boresight():
         # Arrange
         lever_arm_components = np.array([0.1, 0.05, -0.02])
         boresight_angles_rad = np.array([0.0, 0.0, 0.0])
         expected_effective_lever_arm = np.array([0.1, 0.05, -0.02])

         # Act
         # from lever_arm_utils import calculate_effective_lever_arm # Actual import
         result = calculate_effective_lever_arm(lever_arm_components, boresight_angles_rad)

         # Assert
         assert np.array_equal(result, expected_effective_lever_arm)
     ```

#### 2.1.2 Test Case: `test_calculate_effective_lever_arm_roll_90_deg`
   - **Objective**: Verify `calculate_effective_lever_arm` with a 90-degree roll.
   - **File to Create/Modify**: [`test_lever_arm.py`](test_lever_arm.py)
   - **Inputs**:
     - `lever_arm_components`: `(1, 2, 3)`
     - `boresight_angles_rad`: `(np.pi/2, 0, 0)` (90-degree roll)
   - **Action**: Call `calculate_effective_lever_arm`.
   - **Expected Output/Behavior**: `(1, -3, 2)` (lx remains, ly becomes -lz, lz becomes ly).
   - **Acceptance Criteria**: `np.allclose(result, expected_result)` is true.
   - **Test Scaffolding**:
     ```python
     import numpy as np
     # from lever_arm_utils import calculate_effective_lever_arm

     def test_calculate_effective_lever_arm_roll_90_deg():
         # Arrange
         lever_arm_components = np.array([1.0, 2.0, 3.0])
         boresight_angles_rad = np.array([np.pi/2, 0.0, 0.0]) # 90 deg roll
         # Expected: R_roll = [[1,0,0],[0,0,-1],[0,1,0]]
         # Effective = R_roll @ lever_arm = [1*1, -1*3, 1*2] = [1, -3, 2]
         expected_effective_lever_arm = np.array([1.0, -3.0, 2.0]) 

         # Act
         # from lever_arm_utils import calculate_effective_lever_arm # Actual import
         result = calculate_effective_lever_arm(lever_arm_components, boresight_angles_rad)

         # Assert
         assert np.allclose(result, expected_effective_lever_arm, atol=1e-7)
     ```
     *(Add similar tests for 90-degree pitch and yaw, and combined angles)*

### 2.2 Test Cases for `main_pipeline.py`

#### 2.2.1 Test Case: `test_main_pipeline_missing_hsi_file`
   - **Objective**: Verify `main()` function in [`main_pipeline.py`](main_pipeline.py) handles a missing HSI file gracefully.
   - **File to Create/Modify**: [`test_main_pipeline.py`](test_main_pipeline.py)
   - **Inputs**:
     - `args`: Command-line arguments pointing to a non-existent HSI file, but valid other files.
   - **Action**: Call `main(args)` (or simulate `argparse` and call the core logic).
   - **Expected Output/Behavior**:
     - The pipeline logs an error.
     - `SystemExit` is raised or the function returns a non-zero exit code/False.
     - No output files are created.
   - **Acceptance Criteria**: `pytest.raises(SystemExit)` or `FileNotFoundError` (depending on implementation) and error logged.
   - **Test Scaffolding**:
     ```python
     import pytest
     from unittest.mock import patch, MagicMock
     # from main_pipeline import main # Adjust import

     @patch('main_pipeline.argparse.ArgumentParser')
     @patch('main_pipeline.get_logger')
     @patch('main_pipeline.run_georeferencing') # To prevent it from running
     @patch('main_pipeline.os.path.exists')
     def test_main_pipeline_missing_hsi_file(self, mock_path_exists, mock_run_georef, mock_get_logger, mock_arg_parser):
         # Arrange
         from main_pipeline import main # Actual import
         
         mock_logger_instance = MagicMock()
         mock_get_logger.return_value = mock_logger_instance
         
         # Simulate command line arguments
         mock_args = MagicMock()
         mock_args.hsi_file = "non_existent.hdr"
         mock_args.poses_file = "dummy_poses.csv"
         mock_args.output_path = "dummy_output.csv"
         # ... other necessary args ...
         mock_args.log_level = "INFO"
         mock_arg_parser.return_value.parse_args.return_value = mock_args

         # os.path.exists for hsi_file returns False, others True
         def side_effect_path_exists(path):
             if path == "non_existent.hdr":
                 return False
             if path.endswith(".hdr"): # For associated .dat file if checked
                 return False
             return True
         mock_path_exists.side_effect = side_effect_path_exists
         
         # Act & Assert
         with pytest.raises(SystemExit) as excinfo: # Or FileNotFoundError if main raises it directly
             main() 
         
         # Assert that SystemExit was called (e.g. by sys.exit(1))
         assert excinfo.value.code != 0 # Or check specific error code if defined

         # Assert an error was logged
         error_log_found = False
         for call_args in mock_logger_instance.error.call_args_list:
             if "HSI file non_existent.hdr not found" in call_args[0][0]:
                 error_log_found = True
                 break
         assert error_log_found, "Error for missing HSI file not logged"
         mock_run_georef.assert_not_called()
     ```
     *(Add similar tests for missing poses file, invalid output path, invalid config values)*

#### 2.2.2 Test Case: `test_main_pipeline_successful_run_flat_plane`
   - **Objective**: Integration test for a successful `main()` run in "flat_plane" mode.
   - **File to Create/Modify**: [`test_main_pipeline.py`](test_main_pipeline.py)
   - **Inputs**:
     - `args`: Valid command-line arguments for all required files (use dummy/temporary files).
     - Configuration for "flat_plane" mode.
   - **Action**: Call `main(args)`.
   - **Expected Output/Behavior**:
     - `run_georeferencing` is called with the correct configuration.
     - The pipeline completes successfully (e.g., `main` returns None or `run_georeferencing` returns True).
     - Expected log messages (e.g., "Pipeline finished successfully") are present.
   - **Acceptance Criteria**: `mock_run_georeferencing.assert_called_once_with(expected_config_dict)` and successful completion.
   - **Test Scaffolding**:
     ```python
     from unittest.mock import patch, MagicMock, ANY
     # from main_pipeline import main, setup_logging # Adjust import

     @patch('main_pipeline.argparse.ArgumentParser')
     @patch('main_pipeline.get_logger') # Mock get_logger to control the logger instance
     @patch('main_pipeline.run_georeferencing')
     @patch('main_pipeline.os.path.exists', return_value=True) # Assume all files exist
     @patch('main_pipeline.os.makedirs')
     def test_main_pipeline_successful_run_flat_plane(self, mock_makedirs, mock_path_exists, 
                                                     mock_run_georef, mock_get_logger, mock_arg_parser):
         # Arrange
         from main_pipeline import main, setup_logging # Actual import

         mock_logger_instance = MagicMock()
         mock_get_logger.return_value = mock_logger_instance
         mock_run_georef.return_value = True # Simulate successful georeferencing

         # Simulate command line arguments
         mock_args = MagicMock()
         mock_args.hsi_file = "dummy.hdr"
         mock_args.poses_file = "dummy_poses.csv"
         mock_args.output_path = "dummy_output.csv"
         mock_args.processing_mode = "flat_plane"
         mock_args.flight_altitude = 100.0
         mock_args.dsm_file = None
         mock_args.sensor_angle_file = "dummy_sensor.csv"
         mock_args.lever_arm_x = 0.0; mock_args.lever_arm_y = 0.0; mock_args.lever_arm_z = 0.0
         mock_args.boresight_roll = 0.0; mock_args.boresight_pitch = 0.0; mock_args.boresight_yaw = 0.0
         mock_args.log_level = "INFO"
         mock_args.vectorized = True
         mock_arg_parser.return_value.parse_args.return_value = mock_args
         
         # Act
         main() # Call the main function

         # Assert
         mock_run_georef.assert_called_once()
         called_config = mock_run_georef.call_args[0][0]
         
         assert called_config['hsi_file_path'] == "dummy.hdr"
         assert called_config['processing_mode'] == "flat_plane"
         assert called_config['flight_altitude_m'] == 100.0
         
         # Assert successful completion log
         success_log_found = False
         for call_args in mock_logger_instance.info.call_args_list:
             if "HSI Georeferencing Pipeline finished successfully" in call_args[0][0]:
                 success_log_found = True
                 break
         assert success_log_found, "Success message not logged"
     ```
     *(Add a similar test for "dsm_mode")*

### 2.3 Further Edge Cases for Georeferencing Logic (e.g., in `georeference_hsi_pixels.py`)

#### 2.3.1 Test Case: `test_parse_sensor_model_empty_file`
   - **Objective**: Verify `parse_sensor_model` handles an empty sensor model file.
   - **File to Modify**: [`test_georeferencing.py`](test_georeferencing.py)
   - **Inputs**: Path to an empty CSV file.
   - **Action**: Call `parse_sensor_model`.
   - **Expected Output/Behavior**: Raises `ValueError` or returns empty/default arrays with a warning.
   - **Acceptance Criteria**: Correct exception raised or default behavior verified + warning logged.
   - **Test Scaffolding**:
    ```python
    import pytest
    import pandas as pd
    from unittest.mock import patch, mock_open
    # from georeference_hsi_pixels import parse_sensor_model

    def test_parse_sensor_model_empty_file(self, caplog): # caplog for capturing logs
        # Arrange
        from georeference_hsi_pixels import parse_sensor_model # Actual import
        
        # Simulate an empty CSV file being read by pandas
        with patch('georeference_hsi_pixels.pd.read_csv', return_value=pd.DataFrame()):
            # Act & Assert
            with pytest.raises(ValueError, match="Sensor model file is empty or invalid"):
                 parse_sensor_model("dummy_empty_sensor.csv")
        
        # OR if it's expected to return defaults and log a warning:
        # with patch('georeference_hsi_pixels.pd.read_csv', return_value=pd.DataFrame()):
        #     vinkelx, vinkely = parse_sensor_model("dummy_empty_sensor.csv")
        #     assert vinkelx.size == 0 # Or whatever default is expected
        #     assert vinkely.size == 0
        #     assert "Sensor model file is empty" in caplog.text 
    ```

#### 2.3.2 Test Case: `test_calculate_ray_dsm_intersection_ray_parallel_to_dsm`
   - **Objective**: Test `calculate_ray_dsm_intersection` where the ray is perfectly parallel to a flat DSM.
   - **File to Modify**: [`test_georeferencing.py`](test_georeferencing.py)
   - **Inputs**:
     - `P_sensor`: e.g., `(0,0,10)`
     - `d_world_normalized`: e.g., `(1,0,0)` (ray along X-axis)
     - `interpolator`: Mock or simple flat plane interpolator at `Z=5`.
   - **Action**: Call `calculate_ray_dsm_intersection`.
   - **Expected Output/Behavior**: Returns `(NaN, NaN, NaN)` as no intersection occurs.
   - **Acceptance Criteria**: All returned coordinates are `np.nan`.
   - **Test Scaffolding**:
    ```python
    import numpy as np
    # from georeference_hsi_pixels import calculate_ray_dsm_intersection
    # from scipy.interpolate import RegularGridInterpolator # If used for mock

    def test_calculate_ray_dsm_intersection_ray_parallel_to_dsm(self, flat_dsm_interpolator_at_z5):
        # Arrange
        from georeference_hsi_pixels import calculate_ray_dsm_intersection # Actual import
        P_sensor = np.array([0.0, 0.0, 10.0])
        d_world_normalized = np.array([1.0, 0.0, 0.0]) # Ray parallel to XY plane
        
        # interpolator returns a constant Z=5, ray is at Z=10 and parallel
        # bounds, nodata_value, max_dist, initial_step, tolerance as per other tests
        bounds = ([0, 100], [0, 100]) 
        nodata_value = -9999
        max_dist = 200
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        X_ground, Y_ground, Z_ground = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, flat_dsm_interpolator_at_z5, 
            bounds, nodata_value, max_dist, initial_step, tolerance
        )

        # Assert
        assert np.isnan(X_ground)
        assert np.isnan(Y_ground)
        assert np.isnan(Z_ground)
    ```

## 3. Refactoring `calculate_ray_dsm_intersection` (Prompt [LS4_3])

This section details test cases related to the refactoring of `calculate_ray_dsm_intersection` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) by extracting a helper function for the ray marching and `brentq` logic.

### 3.1 Test Case: `test_existing_calculate_ray_dsm_intersection_tests_pass`
   - **Objective**: Ensure all existing unit tests for `calculate_ray_dsm_intersection` (from [`test_georeferencing.py`](test_georeferencing.py)) continue to pass after the internal refactoring.
   - **File to Modify**: No new test code. This is a meta-test/verification step.
   - **Inputs**: Existing test suite for `calculate_ray_dsm_intersection`.
   - **Action**: Run the existing test suite against the refactored code.
   - **Expected Output/Behavior**: All existing tests for `calculate_ray_dsm_intersection` pass.
   - **Acceptance Criteria**: 100% pass rate for tests tagged or named as targeting `calculate_ray_dsm_intersection`.
   - **Test Scaffolding**: Not applicable (uses existing tests).

### 3.2 Test Case (Optional): `test_internal_find_intersection_point_with_dsm_direct`
   - **Objective**: If the new private helper function (e.g., `_find_intersection_point_with_dsm`) is sufficiently complex, add direct unit tests for it. This example assumes a scenario where the ray starts below the DSM and should find an intersection.
   - **File to Create/Modify**: [`test_georeferencing.py`](test_georeferencing.py) (or a new file if preferred for private function tests).
   - **Inputs for `_find_intersection_point_with_dsm`**:
     - `P_sensor`: e.g., `(0,0,0)`
     - `d_world_normalized`: e.g., `(0,0,1)` (ray pointing straight up)
     - `func_to_solve`: A lambda or function `lambda t: (P_sensor + t * d_world_normalized)[2] - get_dsm_z_func(P_sensor + t * d_world_normalized)`
     - `get_dsm_z_func`: A mock or simple function that returns DSM Z values (e.g., a flat plane at `Z=10`).
     - `t_start`, `diff_start`: Initial distance along ray and Z-difference.
     - `max_dist`, `initial_step`, `tolerance_brentq`: Control parameters.
   - **Action**: Call `_find_intersection_point_with_dsm` with these inputs.
   - **Expected Output/Behavior**: Returns the correct intersection coordinates `(X_ground, Y_ground, Z_ground)`, e.g., `(0,0,10)`.
   - **Acceptance Criteria**: Returned coordinates match expected values within a small tolerance.
   - **Test Scaffolding**:
     ```python
     import numpy as np
     import pytest
     # from georeference_hsi_pixels import _find_intersection_point_with_dsm # Adjust import

     def test_internal_find_intersection_point_with_dsm_direct_hit(self, flat_dsm_interpolator_at_z10):
         # Arrange
         # This test assumes _find_intersection_point_with_dsm is accessible for testing
         # It might require making it a non-private function or using specific test setups.
         # For now, we'll assume it can be imported or accessed.
         from georeference_hsi_pixels import _find_intersection_point_with_dsm # Actual import
         
         P_sensor = np.array([0.0, 0.0, 0.0])
         d_world_normalized = np.array([0.0, 0.0, 1.0]) # Ray pointing straight up

         # DSM is a flat plane at Z=10
         # get_dsm_z_func will use flat_dsm_interpolator_at_z10
         def get_dsm_z_for_test(point_on_ray):
             # flat_dsm_interpolator_at_z10 expects a list of points for interpolation
             # For a single point, wrap it.
             return flat_dsm_interpolator_at_z10([[point_on_ray[0], point_on_ray[1]]])[0]

         def func_to_solve_for_test(t):
             point_on_ray = P_sensor + t * d_world_normalized
             return point_on_ray[2] - get_dsm_z_for_test(point_on_ray)

         # Initial check: P_sensor is at Z=0, DSM at Z=10. Ray points towards DSM.
         t_initial = 0.0 
         diff_initial = func_to_solve_for_test(t_initial) # Should be 0 - 10 = -10

         max_dist = 20.0
         initial_step = 0.5 # Small enough to find the interval
         tolerance_brentq = 1e-7
         
         expected_intersection = np.array([0.0, 0.0, 10.0])

         # Act
         # Note: The signature of _find_intersection_point_with_dsm might vary based on actual refactoring.
         # This is based on the conceptual signature from prompts_LS4.md.
         # def _perform_ray_marching_and_brentq(P_sensor, d_world_normalized, func_to_solve, get_dsm_z_func, 
         #                                    t_initial, diff_initial, max_dist, initial_step, tolerance_brentq):
         
         X_ground, Y_ground, Z_ground_coord = _find_intersection_point_with_dsm(
             P_sensor, d_world_normalized, func_to_solve_for_test, get_dsm_z_for_test,
             t_initial, diff_initial, max_dist, initial_step, tolerance_brentq
         )
         
         # Assert
         assert np.allclose(np.array([X_ground, Y_ground, Z_ground_coord]), expected_intersection, atol=tolerance_brentq)
     ```
     *(Add more direct tests for the helper: no intersection, ray starts above DSM and points away, brentq error scenarios if distinguishable at this level).*

### 3.3 Test Case: `test_calculate_ray_dsm_intersection_docstring_exists`
   - **Objective**: Verify that the refactored `calculate_ray_dsm_intersection` and the new helper function (`_find_intersection_point_with_dsm` or similar) have non-empty docstrings.
   - **File to Modify**: [`test_georeferencing.py`](test_georeferencing.py)
   - **Inputs**: None.
   - **Action**: Inspect the `__doc__` attribute of the functions.
   - **Expected Output/Behavior**: Docstrings are present and not empty.
   - **Acceptance Criteria**: `function.__doc__ is not None and len(function.__doc__.strip()) > 0`.
   - **Test Scaffolding**:
     ```python
     # from georeference_hsi_pixels import calculate_ray_dsm_intersection, _find_intersection_point_with_dsm 

     def test_calculate_ray_dsm_intersection_docstring_exists(self):
         # Arrange
         from georeference_hsi_pixels import calculate_ray_dsm_intersection # Actual import
         # Act & Assert
         assert calculate_ray_dsm_intersection.__doc__ is not None
         assert len(calculate_ray_dsm_intersection.__doc__.strip()) > 20 # Arbitrary length check

     def test_helper_find_intersection_docstring_exists(self):
         # Arrange
         from georeference_hsi_pixels import _find_intersection_point_with_dsm # Actual import (or whatever it's named)
         # Act & Assert
         assert _find_intersection_point_with_dsm.__doc__ is not None
         assert len(_find_intersection_point_with_dsm.__doc__.strip()) > 20 # Arbitrary length check
     ```

## 4. Style and Documentation Enhancements (Prompt [LS4_4])

This section details test cases for style and documentation improvements, primarily focusing on [`vectorized_georef.py`](vectorized_georef.py) and docstrings of modified functions.

### 4.1 Test Case: `test_vectorized_georef_imports_are_top_level`
   - **Objective**: Verify that `PoseTransformationError` and `VectorizedProcessingError` are imported at the top level of [`vectorized_georef.py`](vectorized_georef.py) and not locally within `except` blocks.
   - **File to Modify**: This is a static analysis check, not a runtime test. It can be framed as a linter rule or a manual check. For TDD, we can write a test that inspects the file content if necessary, or rely on linters.
   - **If a programmatic test is desired (e.g., for CI validation without full linting):**
     - **File to Create/Modify**: A new test file, e.g., `test_code_style.py`.
     - **Inputs**: Path to [`vectorized_georef.py`](vectorized_georef.py).
     - **Action**: Read the file content and use regex to check for local imports of these specific exceptions within function bodies (especially `except` blocks).
     - **Expected Output/Behavior**: No local imports of these exceptions are found. Top-level imports are present.
     - **Acceptance Criteria**: Regex search for `from pipeline_exceptions import PoseTransformationError` (and `VectorizedProcessingError`) inside function definitions returns no matches. A search for top-level imports finds them.
   - **Test Scaffolding (Conceptual - for file content check)**:
     ```python
     import re

     def test_vectorized_georef_imports_are_top_level():
         # Arrange
         file_path = "vectorized_georef.py" # Adjust path as needed
         with open(file_path, 'r') as f:
             content = f.read()

         # Check for local imports within function/method definitions
         # This regex is basic and might need refinement
         local_import_pattern_pose = r"def\s+\w+\(.*\):\s*([\s\S]*?)except\s+.*:\s*from\s+pipeline_exceptions\s+import\s+PoseTransformationError"
         local_import_pattern_vec = r"def\s+\w+\(.*\):\s*([\s\S]*?)except\s+.*:\s*from\s+pipeline_exceptions\s+import\s+VectorizedProcessingError"
         
         top_level_import_pattern_pose = r"^from\s+pipeline_exceptions\s+import\s+.*PoseTransformationError"
         top_level_import_pattern_vec = r"^from\s+pipeline_exceptions\s+import\s+.*VectorizedProcessingError"

         # Act & Assert
         assert not re.search(local_import_pattern_pose, content, re.MULTILINE), \
             "Local import of PoseTransformationError found in vectorized_georef.py"
         assert not re.search(local_import_pattern_vec, content, re.MULTILINE), \
             "Local import of VectorizedProcessingError found in vectorized_georef.py"

         assert re.search(top_level_import_pattern_pose, content, re.MULTILINE), \
             "Top-level import of PoseTransformationError not found in vectorized_georef.py"
         assert re.search(top_level_import_pattern_vec, content, re.MULTILINE), \
             "Top-level import of VectorizedProcessingError not found in vectorized_georef.py"
     ```
     *Note: This kind of test is brittle. Linters like Flake8 (e.g., with `flake8-import-order` or by checking for `E402 module level import not at top of file` if imports are moved inside functions) are generally better for style.*

### 4.2 Test Case: `test_docstrings_for_LS4_modified_functions`
   - **Objective**: Verify that all functions modified or created as part of LS4 prompts (LS4_1, LS4_2, LS4_3) have non-empty, reasonably descriptive docstrings.
   - **File to Modify**: [`test_main_pipeline.py`](test_main_pipeline.py), [`test_georeferencing.py`](test_georeferencing.py), [`test_lever_arm.py`](test_lever_arm.py), [`test_vectorized_georef.py`](test_vectorized_georef.py).
   - **Inputs**: None.
   - **Action**: Inspect the `__doc__` attribute of relevant functions.
   - **Expected Output/Behavior**: Docstrings are present and meet a minimum length/content quality.
   - **Acceptance Criteria**: `function.__doc__ is not None and len(function.__doc__.strip()) > N` (where N is a reasonable minimum length, e.g., 20-50 characters).
   - **Test Scaffolding (Example for one function - repeat for others)**:
     ```python
     # In test_vectorized_georef.py
     # from vectorized_georef import process_hsi_line_vectorized # (if modified for LS4_1)

     def test_process_hsi_line_vectorized_docstring_updated_for_ls4(): # Assuming it was touched
         # Arrange
         from vectorized_georef import process_hsi_line_vectorized # Actual import
         # Act & Assert
         assert process_hsi_line_vectorized.__doc__ is not None
         assert len(process_hsi_line_vectorized.__doc__.strip()) > 50 # Check for substantial docstring
         # Could add checks for specific keywords if parameters/returns changed significantly
         assert "PoseTransformationError" in process_hsi_line_vectorized.__doc__ # If it now mentions this
     ```
     *(This would be repeated for functions like `calculate_effective_lever_arm`, `main` in `main_pipeline.py` if its core logic changed, etc., and any new helper functions created in LS4_3).*

### 4.3 Test Case: `test_main_pipeline_documentation_md_updated_if_needed`
   - **Objective**: This is a placeholder for a manual check or a process reminder. If LS4 changes significantly alter pipeline behavior, configuration, or outputs, [`main_pipeline_documentation.md`](main_pipeline_documentation.md) should be updated.
   - **File to Modify**: Not a test code.
   - **Acceptance Criteria**: Manual review confirms documentation is consistent with LS4 changes.
   - **Test Scaffolding**: Not applicable for automated test. Could be a checklist item.

## Conclusion

These test specifications aim to cover the requirements of LS4, ensuring that fixes are verified, coverage is improved, refactoring is supported by tests, and documentation/style enhancements are addressed. The scaffolding provides a starting point for implementing these tests.

## 3. Refactoring `calculate_ray_dsm_intersection` (Prompt [LS4_3])

This section details test cases related to the refactoring of `calculate_ray_dsm_intersection` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) by extracting a helper function for the ray marching and `brentq` logic.

### 3.1 Test Case: `test_existing_calculate_ray_dsm_intersection_tests_pass`
   - **Objective**: Ensure all existing unit tests for `calculate_ray_dsm_intersection` (from [`test_georeferencing.py`](test_georeferencing.py)) continue to pass after the internal refactoring.
   - **File to Modify**: No new test code. This is a meta-test/verification step.
   - **Inputs**: Existing test suite for `calculate_ray_dsm_intersection`.
   - **Action**: Run the existing test suite against the refactored code.
   - **Expected Output/Behavior**: All existing tests for `calculate_ray_dsm_intersection` pass.
   - **Acceptance Criteria**: 100% pass rate for tests tagged or named as targeting `calculate_ray_dsm_intersection`.
   - **Test Scaffolding**: Not applicable (uses existing tests).

### 3.2 Test Case (Optional): `test_internal_find_intersection_point_with_dsm_direct`
   - **Objective**: If the new private helper function (e.g., `_find_intersection_point_with_dsm`) is sufficiently complex, add direct unit tests for it. This example assumes a scenario where the ray starts below the DSM and should find an intersection.
   - **File to Create/Modify**: [`test_georeferencing.py`](test_georeferencing.py) (or a new file if preferred for private function tests).
   - **Inputs for `_find_intersection_point_with_dsm`**:
     - `P_sensor`: e.g., `(0,0,0)`
     - `d_world_normalized`: e.g., `(0,0,1)` (ray pointing straight up)
     - `func_to_solve`: A lambda or function `lambda t: (P_sensor + t * d_world_normalized)[2] - get_dsm_z_func(P_sensor + t * d_world_normalized)`
     - `get_dsm_z_func`: A mock or simple function that returns DSM Z values (e.g., a flat plane at `Z=10`).
     - `t_start`, `diff_start`: Initial distance along ray and Z-difference.
     - `max_dist`, `initial_step`, `tolerance_brentq`: Control parameters.
   - **Action**: Call `_find_intersection_point_with_dsm` with these inputs.
   - **Expected Output/Behavior**: Returns the correct intersection coordinates `(X_ground, Y_ground, Z_ground)`, e.g., `(0,0,10)`.
   - **Acceptance Criteria**: Returned coordinates match expected values within a small tolerance.
   - **Test Scaffolding**:
     ```python
     import numpy as np
     import pytest
     # from georeference_hsi_pixels import _find_intersection_point_with_dsm # Adjust import

     def test_internal_find_intersection_point_with_dsm_direct_hit(self, flat_dsm_interpolator_at_z10):
         # Arrange
         # This test assumes _find_intersection_point_with_dsm is accessible for testing
         # It might require making it a non-private function or using specific test setups.
         # For now, we'll assume it can be imported or accessed.
         from georeference_hsi_pixels import _find_intersection_point_with_dsm # Actual import
         
         P_sensor = np.array([0.0, 0.0, 0.0])
         d_world_normalized = np.array([0.0, 0.0, 1.0]) # Ray pointing straight up

         # DSM is a flat plane at Z=10
         # get_dsm_z_func will use flat_dsm_interpolator_at_z10
         def get_dsm_z_for_test(point_on_ray):
             # flat_dsm_interpolator_at_z10 expects a list of points for interpolation
             # For a single point, wrap it.
             return flat_dsm_interpolator_at_z10([[point_on_ray[0], point_on_ray[1]]])[0]

         def func_to_solve_for_test(t):
             point_on_ray = P_sensor + t * d_world_normalized
             return point_on_ray[2] - get_dsm_z_for_test(point_on_ray)

         # Initial check: P_sensor is at Z=0, DSM at Z=10. Ray points towards DSM.
         t_initial = 0.0 
         diff_initial = func_to_solve_for_test(t_initial) # Should be 0 - 10 = -10

         max_dist = 20.0
         initial_step = 0.5 # Small enough to find the interval
         tolerance_brentq = 1e-7
         
         expected_intersection = np.array([0.0, 0.0, 10.0])

         # Act
         # Note: The signature of _find_intersection_point_with_dsm might vary based on actual refactoring.
         # This is based on the conceptual signature from prompts_LS4.md.
         # def _perform_ray_marching_and_brentq(P_sensor, d_world_normalized, func_to_solve, get_dsm_z_func, 
         #                                    t_initial, diff_initial, max_dist, initial_step, tolerance_brentq):
         
         X_ground, Y_ground, Z_ground_coord = _find_intersection_point_with_dsm(
             P_sensor, d_world_normalized, func_to_solve_for_test, get_dsm_z_for_test,
             t_initial, diff_initial, max_dist, initial_step, tolerance_brentq
         )
         
         # Assert
         assert np.allclose(np.array([X_ground, Y_ground, Z_ground_coord]), expected_intersection, atol=tolerance_brentq)
     ```
     *(Add more direct tests for the helper: no intersection, ray starts above DSM and points away, brentq error scenarios if distinguishable at this level).*

### 3.3 Test Case: `test_calculate_ray_dsm_intersection_docstring_exists`
   - **Objective**: Verify that the refactored `calculate_ray_dsm_intersection` and the new helper function (`_find_intersection_point_with_dsm` or similar) have non-empty docstrings.
   - **File to Modify**: [`test_georeferencing.py`](test_georeferencing.py)
   - **Inputs**: None.
   - **Action**: Inspect the `__doc__` attribute of the functions.
   - **Expected Output/Behavior**: Docstrings are present and not empty.
   - **Acceptance Criteria**: `function.__doc__ is not None and len(function.__doc__.strip()) > 0`.
   - **Test Scaffolding**:
     ```python
     # from georeference_hsi_pixels import calculate_ray_dsm_intersection, _find_intersection_point_with_dsm 

     def test_calculate_ray_dsm_intersection_docstring_exists(self):
         # Arrange
         from georeference_hsi_pixels import calculate_ray_dsm_intersection # Actual import
         # Act & Assert
         assert calculate_ray_dsm_intersection.__doc__ is not None
         assert len(calculate_ray_dsm_intersection.__doc__.strip()) > 20 # Arbitrary length check

     def test_helper_find_intersection_docstring_exists(self):
         # Arrange
         from georeference_hsi_pixels import _find_intersection_point_with_dsm # Actual import (or whatever it's named)
         # Act & Assert
         assert _find_intersection_point_with_dsm.__doc__ is not None
         assert len(_find_intersection_point_with_dsm.__doc__.strip()) > 20 # Arbitrary length check
     ```

## 4. Style and Documentation Enhancements (Prompt [LS4_4])

This section details test cases for style and documentation improvements, primarily focusing on [`vectorized_georef.py`](vectorized_georef.py) and docstrings of modified functions.

### 4.1 Test Case: `test_vectorized_georef_imports_are_top_level`
   - **Objective**: Verify that `PoseTransformationError` and `VectorizedProcessingError` are imported at the top level of [`vectorized_georef.py`](vectorized_georef.py) and not locally within `except` blocks.
   - **File to Modify**: This is a static analysis check, not a runtime test. It can be framed as a linter rule or a manual check. For TDD, we can write a test that inspects the file content if necessary, or rely on linters.
   - **If a programmatic test is desired (e.g., for CI validation without full linting):**
     - **File to Create/Modify**: A new test file, e.g., `test_code_style.py`.
     - **Inputs**: Path to [`vectorized_georef.py`](vectorized_georef.py).
     - **Action**: Read the file content and use regex to check for local imports of these specific exceptions within function bodies (especially `except` blocks).
     - **Expected Output/Behavior**: No local imports of these exceptions are found. Top-level imports are present.
     - **Acceptance Criteria**: Regex search for `from pipeline_exceptions import PoseTransformationError` (and `VectorizedProcessingError`) inside function definitions returns no matches. A search for top-level imports finds them.
   - **Test Scaffolding (Conceptual - for file content check)**:
     ```python
     import re

     def test_vectorized_georef_imports_are_top_level():
         # Arrange
         file_path = "vectorized_georef.py" # Adjust path as needed
         with open(file_path, 'r') as f:
             content = f.read()

         # Check for local imports within function/method definitions
         # This regex is basic and might need refinement
         local_import_pattern_pose = r"def\s+\w+\(.*\):\s*([\s\S]*?)except\s+.*:\s*from\s+pipeline_exceptions\s+import\s+PoseTransformationError"
         local_import_pattern_vec = r"def\s+\w+\(.*\):\s*([\s\S]*?)except\s+.*:\s*from\s+pipeline_exceptions\s+import\s+VectorizedProcessingError"
         
         top_level_import_pattern_pose = r"^from\s+pipeline_exceptions\s+import\s+.*PoseTransformationError"
         top_level_import_pattern_vec = r"^from\s+pipeline_exceptions\s+import\s+.*VectorizedProcessingError"

         # Act & Assert
         assert not re.search(local_import_pattern_pose, content, re.MULTILINE), \
             "Local import of PoseTransformationError found in vectorized_georef.py"
         assert not re.search(local_import_pattern_vec, content, re.MULTILINE), \
             "Local import of VectorizedProcessingError found in vectorized_georef.py"

         assert re.search(top_level_import_pattern_pose, content, re.MULTILINE), \
             "Top-level import of PoseTransformationError not found in vectorized_georef.py"
         assert re.search(top_level_import_pattern_vec, content, re.MULTILINE), \
             "Top-level import of VectorizedProcessingError not found in vectorized_georef.py"
     ```
     *Note: This kind of test is brittle. Linters like Flake8 (e.g., with `flake8-import-order` or by checking for `E402 module level import not at top of file` if imports are moved inside functions) are generally better for style.*

### 4.2 Test Case: `test_docstrings_for_LS4_modified_functions`
   - **Objective**: Verify that all functions modified or created as part of LS4 prompts (LS4_1, LS4_2, LS4_3) have non-empty, reasonably descriptive docstrings.
   - **File to Modify**: [`test_main_pipeline.py`](test_main_pipeline.py), [`test_georeferencing.py`](test_georeferencing.py), [`test_lever_arm.py`](test_lever_arm.py), [`test_vectorized_georef.py`](test_vectorized_georef.py).
   - **Inputs**: None.
   - **Action**: Inspect the `__doc__` attribute of relevant functions.
   - **Expected Output/Behavior**: Docstrings are present and meet a minimum length/content quality.
   - **Acceptance Criteria**: `function.__doc__ is not None and len(function.__doc__.strip()) > N` (where N is a reasonable minimum length, e.g., 20-50 characters).
   - **Test Scaffolding (Example for one function - repeat for others)**:
     ```python
     # In test_vectorized_georef.py
     # from vectorized_georef import process_hsi_line_vectorized # (if modified for LS4_1)

     def test_process_hsi_line_vectorized_docstring_updated_for_ls4(): # Assuming it was touched
         # Arrange
         from vectorized_georef import process_hsi_line_vectorized # Actual import
         # Act & Assert
         assert process_hsi_line_vectorized.__doc__ is not None
         assert len(process_hsi_line_vectorized.__doc__.strip()) > 50 # Check for substantial docstring
         # Could add checks for specific keywords if parameters/returns changed significantly
         assert "PoseTransformationError" in process_hsi_line_vectorized.__doc__ # If it now mentions this
     ```
     *(This would be repeated for functions like `calculate_effective_lever_arm`, `main` in `main_pipeline.py` if its core logic changed, etc., and any new helper functions created in LS4_3).*

### 4.3 Test Case: `test_main_pipeline_documentation_md_updated_if_needed`
   - **Objective**: This is a placeholder for a manual check or a process reminder. If LS4 changes significantly alter pipeline behavior, configuration, or outputs, [`main_pipeline_documentation.md`](main_pipeline_documentation.md) should be updated.
   - **File to Modify**: Not a test code.
   - **Acceptance Criteria**: Manual review confirms documentation is consistent with LS4 changes.
   - **Test Scaffolding**: Not applicable for automated test. Could be a checklist item.

## Conclusion

These test specifications aim to cover the requirements of LS4, ensuring that fixes are verified, coverage is improved, refactoring is supported by tests, and documentation/style enhancements are addressed. The scaffolding provides a starting point for implementing these tests.

## 3. Refactoring `calculate_ray_dsm_intersection` (Prompt [LS4_3])

This section details test cases related to the refactoring of `calculate_ray_dsm_intersection` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) by extracting a helper function for the ray marching and `brentq` logic.

### 3.1 Test Case: `test_existing_calculate_ray_dsm_intersection_tests_pass_after_refactor`
   - **Objective**: Ensure all existing unit tests for `calculate_ray_dsm_intersection` (from [`test_georeferencing.py`](test_georeferencing.py)) continue to pass after the internal refactoring where the core ray marching and `brentq` logic is extracted into a helper.
   - **File to Modify**: No new test code specifically for this. This involves running existing tests.
   - **Inputs**: The existing test suite that targets `calculate_ray_dsm_intersection`.
   - **Action**: Execute all tests in [`test_georeferencing.py`](test_georeferencing.py) that cover `calculate_ray_dsm_intersection` against the refactored version of [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
   - **Expected Output/Behavior**: All pre-existing tests for `calculate_ray_dsm_intersection` must pass without modification.
   - **Acceptance Criteria**: 100% pass rate for the relevant subset of existing tests.
   - **Test Scaffolding**: Not applicable (relies on existing tests). The primary check is that the refactoring doesn't break existing, validated behavior.

### 3.2 Test Case (Conditional): Direct Tests for `_find_intersection_point_with_dsm` (New Helper)
   - **Objective**: If the new private helper function (e.g., `_find_intersection_point_with_dsm` or `_perform_ray_marching_and_brentq` as per [`prompts_LS4.md`](prompts_LS4.md)) is deemed sufficiently complex and its logic is not entirely covered by testing the parent `calculate_ray_dsm_intersection`, add direct unit tests. These tests would focus on the specific responsibilities of the helper.
   - **File to Create/Modify**: [`test_georeferencing.py`](test_georeferencing.py)
   - **General Inputs for Helper**:
     - `P_sensor`, `d_world_normalized`
     - `func_to_solve` (the Z-difference function)
     - `get_dsm_z_func` (DSM height retrieval function)
     - `t_initial`, `diff_initial` (initial ray parameter and Z-difference)
     - `max_dist`, `initial_step`, `tolerance_brentq`
   - **Scenarios to Test Directly (if needed)**:
     1.  **Successful Intersection Found**: Ray intersects DSM within `max_dist`.
         - Expected Output: `(X_ground, Y_ground, Z_ground_coord)`
     2.  **No Intersection (Ray Misses)**: Ray passes by DSM or `max_dist` is reached before intersection.
         - Expected Output: `(np.nan, np.nan, np.nan)`
     3.  **Brentq Bracketing Issues**: Scenarios where `brentq` might fail to find a root if the initial bracketing logic within the helper is flawed (e.g., `diff_curr` and `diff_prev` never change sign).
         - Expected Output: `(np.nan, np.nan, np.nan)` and appropriate logging if applicable.
     4.  **Edge Case `t_search` at `max_dist`**: Behavior when the loop terminates exactly at `max_dist`.
   - **Test Scaffolding (Example for Successful Intersection - adapt based on actual helper signature)**:
     ```python
     import numpy as np
     import pytest
     # from georeference_hsi_pixels import _find_intersection_point_with_dsm # Actual name of helper

     # Assuming a fixture 'flat_dsm_interpolator_at_z10' exists that provides
     # a simple interpolator for a flat DSM at Z=10.
     
     # @pytest.mark.skipif(True, reason="Direct helper tests might be covered by parent function tests")
     def test_helper_find_intersection_direct_hit(self, flat_dsm_interpolator_at_z10):
         # Arrange
         # This test assumes the helper function is accessible for testing.
         from georeference_hsi_pixels import _find_intersection_point_with_dsm # Use actual name

         P_sensor = np.array([0.0, 0.0, 0.0])
         d_world_normalized = np.array([0.0, 0.0, 1.0]) # Ray pointing straight up

         def get_dsm_z_for_test(point_on_ray_coords): # Accepts [[x,y]] or [x,y]
             # Ensure input is in the format expected by the interpolator
             coords_to_interpolate = np.array(point_on_ray_coords)
             if coords_to_interpolate.ndim == 1:
                 coords_to_interpolate = np.array([coords_to_interpolate[:2]]) # Interpolator expects list of points
             else:
                 coords_to_interpolate = coords_to_interpolate[:,:2]
             return flat_dsm_interpolator_at_z10(coords_to_interpolate)[0]

         def func_to_solve_for_test(t_param, p_sensor_param, d_world_norm_param, get_dsm_z_func_param):
             point_on_ray = p_sensor_param + t_param * d_world_norm_param
             return point_on_ray[2] - get_dsm_z_func_param(point_on_ray)

         t_initial = 0.0
         # Pass the actual functions to the helper, not the results of their calls
         diff_initial = func_to_solve_for_test(t_initial, P_sensor, d_world_normalized, get_dsm_z_for_test) # Expected: 0 - 10 = -10

         max_dist = 20.0
         initial_step = 0.5 
         tolerance_brentq = 1e-7
         expected_intersection_coords = np.array([0.0, 0.0, 10.0])

         # Act
         # Signature based on prompt: _perform_ray_marching_and_brentq(P_sensor, d_world_normalized, func_to_solve, get_dsm_z_func, t_initial, diff_initial, max_dist, initial_step, tolerance_brentq)
         # Adapt to the actual implemented helper signature.
         result_coords = _find_intersection_point_with_dsm(
             P_sensor, d_world_normalized, 
             lambda t: func_to_solve_for_test(t, P_sensor, d_world_normalized, get_dsm_z_for_test), # Pass a callable func_to_solve
             get_dsm_z_for_test, # Pass the callable get_dsm_z function
             t_initial, diff_initial, max_dist, initial_step, tolerance_brentq
         )
         
         # Assert
         assert result_coords is not None, "Intersection was expected"
         assert np.allclose(np.array(result_coords), expected_intersection_coords, atol=tolerance_brentq)
     ```

### 3.3 Test Case: `test_docstrings_for_refactored_dsm_functions`
   - **Objective**: Verify that the refactored `calculate_ray_dsm_intersection` and the new helper function (e.g., `_find_intersection_point_with_dsm`) have comprehensive and accurate docstrings.
   - **File to Modify**: [`test_georeferencing.py`](test_georeferencing.py)
   - **Inputs**: None.
   - **Action**: Inspect the `__doc__` attribute of `calculate_ray_dsm_intersection` and the new helper function.
   - **Expected Output/Behavior**: Docstrings are present, non-empty, and accurately describe parameters, return values, logic, and any exceptions.
   - **Acceptance Criteria**:
     - `function.__doc__ is not None and len(function.__doc__.strip()) > 50` (arbitrary substantial length).
     - Docstring content for `calculate_ray_dsm_intersection` reflects its simplified role (setup and call to helper).
     - Docstring for the helper function details its specific parameters (e.g., `func_to_solve`, `get_dsm_z_func`, `t_initial`, etc.) and its core ray-marching/`brentq` logic.
   - **Test Scaffolding**:
     ```python
     # In test_georeferencing.py
     # from georeference_hsi_pixels import calculate_ray_dsm_intersection, _find_intersection_point_with_dsm # Adjust actual names

     def test_refactored_calculate_ray_dsm_intersection_docstring(self):
         from georeference_hsi_pixels import calculate_ray_dsm_intersection # Actual import
         docstring = calculate_ray_dsm_intersection.__doc__
         assert docstring is not None and len(docstring.strip()) > 50
         assert "Calls the internal helper" in docstring or "_find_intersection_point_with_dsm" in docstring # Check for mention of helper

     def test_new_helper_dsm_intersection_docstring(self):
         from georeference_hsi_pixels import _find_intersection_point_with_dsm # Actual import and name
         docstring = _find_intersection_point_with_dsm.__doc__
         assert docstring is not None and len(docstring.strip()) > 50
         # Check for key parameters mentioned in the docstring
         assert "P_sensor" in docstring
         assert "d_world_normalized" in docstring
         assert "func_to_solve" in docstring
         assert "get_dsm_z_func" in docstring
         assert "brentq" in docstring # Should explain its role or usage
     ```

## 4. Style and Documentation Enhancements (Prompt [LS4_4])

This section details test cases (mostly static checks or reminders) for style and documentation improvements.

### 4.1 Test Case: `check_vectorized_georef_imports_are_top_level`
   - **Objective**: Verify that `PoseTransformationError` and `VectorizedProcessingError` are imported at the top level of [`vectorized_georef.py`](vectorized_georef.py), not locally.
   - **File to Modify**: This is primarily a static analysis/linting check. A programmatic test is provided for completeness but might be fragile.
   - **Test Scaffolding (Conceptual Python script for checking, not a pytest case unless adapted)**:
     ```python
     import re
     import os

     def check_vectorized_georef_imports():
         # This assumes the script is run from a context where 'vectorized_georef.py' is findable
         # or the path is adjusted.
         file_path = os.path.join(os.path.dirname(__file__), "..", "vectorized_georef.py") # Example path adjustment
         if not os.path.exists(file_path):
             print(f"File not found: {file_path}")
             return False

         with open(file_path, 'r') as f:
             content = f.read()

         # Regex to find imports of specific exceptions inside function/method definitions
         # Looks for 'def ...(...): ... from pipeline_exceptions import ...'
         local_import_pattern = re.compile(
             r"^\s*def\s+\w+\s*\(.*?\):\s*.*?from\s+pipeline_exceptions\s+import\s+(?:PoseTransformationError|VectorizedProcessingError)",
             re.DOTALL | re.MULTILINE
         )
         
         # Regex to find top-level imports of these exceptions
         top_level_import_pattern = re.compile(
             r"^from\s+pipeline_exceptions\s+import.*(PoseTransformationError|VectorizedProcessingError)",
             re.MULTILINE
         )

         local_imports_found = bool(local_import_pattern.search(content))
         top_level_imports_found = bool(top_level_import_pattern.search(content))

         if local_imports_found:
             print("Error: Local import of PoseTransformationError or VectorizedProcessingError found in vectorized_georef.py")
         if not top_level_imports_found:
             print("Error: Top-level import of PoseTransformationError or VectorizedProcessingError NOT found in vectorized_georef.py")
             
         return not local_imports_found and top_level_imports_found

     # Example usage (outside pytest usually):
     # if __name__ == "__main__":
     #     if check_vectorized_georef_imports():
     #         print("Import style for exceptions in vectorized_georef.py is correct.")
     #     else:
     #         print("Import style issues found in vectorized_georef.py.")
     ```
   - **Acceptance Criteria**: Manual review or linter (e.g., Flake8 with `E402`) confirms no local imports of these exceptions in `except` blocks. They are present at the module's top level.

### 4.2 Test Case: `verify_docstrings_for_all_LS4_modified_code`
   - **Objective**: Ensure all functions modified or created as part of *any* LS4 prompt (LS4_1, LS4_2, LS4_3, LS4_4) have clear, accurate, and comprehensive docstrings.
   - **File to Modify**: This is a general verification. Specific docstring tests like 3.3 can be part of this.
   - **Action**: Review docstrings of all functions touched in LS4.
   - **Expected Output/Behavior**: Docstrings describe parameters, return values, key logic, and exceptions.
   - **Acceptance Criteria**: Manual review confirms high-quality docstrings for all affected functions. For critical functions, specific content checks (like parameter names, exception types mentioned) can be added to programmatic tests (see 3.3).

### 4.3 Test Case: `review_main_pipeline_documentation_md`
   - **Objective**: Placeholder for a manual review. If LS4 changes significantly alter pipeline behavior, configuration, or outputs, [`main_pipeline_documentation.md`](main_pipeline_documentation.md) must be updated.
   - **File to Modify**: [`main_pipeline_documentation.md`](main_pipeline_documentation.md) (if needed).
   - **Acceptance Criteria**: Manual review confirms [`main_pipeline_documentation.md`](main_pipeline_documentation.md) is consistent with any LS4 changes.
   - **Test Scaffolding**: Not applicable for an automated test. This is a process step.

## Conclusion

These test specifications aim to cover the requirements of LS4, ensuring that fixes are verified, coverage is improved, refactoring is supported by tests, and documentation/style enhancements are addressed. The scaffolding provides a starting point for implementing these tests. The successful execution of these tests, along with the implementation of the corresponding code changes, will signify the completion of Layer LS4 objectives.
