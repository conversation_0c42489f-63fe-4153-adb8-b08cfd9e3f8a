import numpy as np
from vectorized_georef import (
    process_hsi_line_vectorized,
    calculate_sensor_view_vectors_vectorized,
    transform_to_world_coordinates_vectorized,
    calculate_flat_plane_intersections_vectorized
)

pose = {
    'pos_x': 0.0, 'pos_y': 0.0, 'pos_z': 100.0,
    'quat_w': 1.0, 'quat_x': 0.0, 'quat_y': 0.0, 'quat_z': 0.0
}
vinkelx_rad_all = np.array([-0.1, 0.0, 0.1])
vinkely_rad_all = np.array([0.0, 0.0, 0.0])
R_sensor_to_body = np.eye(3)
effective_lever_arm_body = np.array([0.0, 0.0, 0.0])

# Debug step by step
print("Step 1: Calculate sensor vectors")
pixel_indices = np.arange(3)
d_sensor_frame = calculate_sensor_view_vectors_vectorized(
    pixel_indices, vinkelx_rad_all, vinkely_rad_all
)
print(f"d_sensor_frame:\n{d_sensor_frame}")

print("\nStep 2: Transform to world coordinates")
# Simulate the transformation matrices
from scipy.spatial.transform import Rotation
P_imu_world = np.array([pose['pos_x'], pose['pos_y'], pose['pos_z']])
q_body_to_world_xyzw = np.array([pose['quat_x'], pose['quat_y'], pose['quat_z'], pose['quat_w']])
R_body_to_world = Rotation.from_quat(q_body_to_world_xyzw).as_matrix()
R_sensor_to_world = R_body_to_world @ R_sensor_to_body
P_sensor_world = P_imu_world + R_body_to_world @ effective_lever_arm_body

d_world = transform_to_world_coordinates_vectorized(d_sensor_frame, R_sensor_to_world)
print(f"d_world:\n{d_world}")

print("\nStep 3: Calculate flat plane intersections")
print(f"d_world Z components: {d_world[:, 2]}")
print("Issue: Z components are positive (pointing up), should be negative for nadir sensor")

# Fix the vectors to point downward
d_world_fixed = d_world.copy()
d_world_fixed[:, 2] = -np.abs(d_world_fixed[:, 2])  # Make Z negative
print(f"d_world_fixed Z components: {d_world_fixed[:, 2]}")

X_ground, Y_ground, Z_ground = calculate_flat_plane_intersections_vectorized(
    P_sensor_world, d_world_fixed, 0.0
)
print(f"P_sensor_world: {P_sensor_world}")
print(f"X_ground: {X_ground}")
print(f"Y_ground: {Y_ground}")
print(f"Z_ground: {Z_ground}")

print("\nStep 4: Full process")
results = process_hsi_line_vectorized(
    line_index=0, pose_data=pose, num_samples=3,
    vinkelx_rad_all=vinkelx_rad_all, vinkely_rad_all=vinkely_rad_all,
    R_sensor_to_body=R_sensor_to_body, effective_lever_arm_body=effective_lever_arm_body,
    z_ground_method='flat_plane', z_ground_flat_plane=0.0
)

for i, result in enumerate(results):
    print(f'Pixel {i}: X={result["X_ground"]}, Y={result["Y_ground"]}, Z={result["Z_ground"]}')
