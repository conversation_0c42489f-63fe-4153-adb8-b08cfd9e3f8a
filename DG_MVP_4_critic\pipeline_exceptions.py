"""
Custom exception classes for the HSI Georeferencing Pipeline.

This module defines specific exception types to enable better error handling
and debugging throughout the pipeline.
"""


class PipelineError(Exception):
    """Base exception class for all pipeline-related errors."""
    pass


class PipelineConfigError(PipelineError):
    """Raised when there are configuration-related errors."""
    pass


class HSIDataError(PipelineError):
    """Raised when there are issues with HSI data files or parsing."""
    pass


class SynchronizationError(PipelineError):
    """Raised when there are errors during pose synchronization."""
    pass


class GeoreferencingError(PipelineError):
    """Raised when there are errors during the georeferencing process."""
    pass


class DSMIntersectionError(GeoreferencingError):
    """Raised when there are errors during DSM intersection calculations."""
    pass


class InputDataError(PipelineError):
    """Raised when input data is invalid or missing."""
    pass


class WebODMDataError(PipelineError):
    """Raised when there are issues with WebODM data processing."""
    pass


class VectorizedProcessingError(PipelineError):
    """Raised when vectorized processing fails and fallback is needed."""
    pass


class PoseTransformationError(PipelineError):
    """Raised when pose transformation calculations fail."""
    pass
