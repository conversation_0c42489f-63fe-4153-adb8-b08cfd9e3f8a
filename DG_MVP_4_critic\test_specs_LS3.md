# Test Specifications and Scaffolding for LS3

## Introduction

This document outlines the test specifications and scaffolding for Layer LS3, based on the requirements in [`prompts_LS3.md`](prompts_LS3.md:1), feedback from [`reflection_LS2.md`](reflection_LS2.md:1), and scores from [`scores_LS2.json`](scores_LS2.json:1). The primary goal is to significantly improve test coverage and address identified issues in the HSI georeferencing pipeline, focusing on the reliability and correctness of the georeferencing algorithms.

## 1. Module: `georeference_hsi_pixels.py`

This module is central to the georeferencing pipeline. The following sections detail test cases for its key functions.

### 1.1. Function: `calculate_ray_dsm_intersection`

This function ([`georeference_hsi_pixels.py:155`](georeference_hsi_pixels.py:155)) is critical for DSM-based georeferencing. Tests will cover various scenarios of ray-DSM interaction. It's anticipated that this function might be refactored (as per Prompt [LS3_2] in [`prompts_LS3.md`](prompts_LS3.md:39)). If helper functions are extracted, they will require their own dedicated unit tests. The tests below target the overall functionality of `calculate_ray_dsm_intersection` as it currently stands or its main refactored equivalent.

**General Setup for `calculate_ray_dsm_intersection` tests:**
Mock objects for `dsm_interpolator` (e.g., a `scipy.interpolate.RectBivariateSpline` instance or a mock behaving like one), `dsm_transform` (an `affine.Affine` object), `dsm_nodata_value`, and `config` dictionary will be used.

#### Test Cases for `calculate_ray_dsm_intersection`

##### 1.1.1. Test Case: Successful Intersection - Ray from Above
*   **Description**: Test a standard scenario where the sensor ray, originating above the DSM, successfully intersects a point on the DSM.
*   **Inputs**:
    *   `P_sensor`: `np.array` representing sensor position (X, Y, Z) clearly above the DSM.
    *   `d_world_normalized`: `np.array` representing normalized ray direction vector pointing towards the DSM (e.g., straight down or at an angle).
    *   `dsm_interpolator`: Mocked or simple `RectBivariateSpline` representing a DSM (e.g., a flat plane at Z=0, or a simple slope).
    *   `dsm_transform`: Appropriate Affine transform for the mock DSM.
    *   `dsm_nodata_value`: A value not returned by the interpolator for the test area.
    *   `config`: Dictionary with keys like `max_dist_iter`, `tolerance_brentq`.
*   **Actions**: Call `calculate_ray_dsm_intersection(P_sensor, d_world_normalized, dsm_interpolator, dsm_transform, dsm_nodata_value, config)`.
*   **Expected Outputs/Behavior**:
    *   Returns a tuple `(X_ground, Y_ground, Z_ground)` representing the intersection point.
    *   The returned `Z_ground` should match the DSM elevation at `(X_ground, Y_ground)` within tolerance.
    *   The point `(X_ground, Y_ground, Z_ground)` should lie on the ray defined by `P_sensor` and `d_world_normalized`.
*   **Acceptance Criteria**:
    *   Intersection point is calculated correctly within a defined tolerance (e.g., `1e-5`).
    *   No exceptions are raised.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    import numpy as np
    from scipy.interpolate import RectBivariateSpline
    from affine import Affine
    # from georeference_hsi_pixels import calculate_ray_dsm_intersection # Assuming direct import or via class

    # Inside TestClass
    def test_calculate_ray_dsm_intersection_successful_from_above(self):
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, -1.0]) # Straight down
        
        # Simple DSM: flat plane at Z=10
        x_coords = np.array([0, 100])
        y_coords = np.array([0, 100])
        z_values = np.array([[10.0, 10.0], [10.0, 10.0]])
        mock_dsm_interpolator = RectBivariateSpline(x_coords, y_coords, z_values, kx=1, ky=1)
        mock_dsm_transform = Affine.translation(0,0) # Or appropriate transform
        mock_dsm_nodata_value = -9999
        mock_config = {'max_dist_iter': 1000.0, 'tolerance_brentq': 1e-6, 'initial_step_check_intersection': 1.0, 'max_iterations_intersection': 100}

        expected_X, expected_Y, expected_Z = 50.0, 50.0, 10.0

        # Act
        # result = calculate_ray_dsm_intersection(P_sensor, d_world_normalized, mock_dsm_interpolator, mock_dsm_transform, mock_dsm_nodata_value, mock_config)

        # Assert
        # self.assertIsNotNone(result)
        # self.assertAlmostEqual(result[0], expected_X, places=5)
        # self.assertAlmostEqual(result[1], expected_Y, places=5)
        # self.assertAlmostEqual(result[2], expected_Z, places=5)
        pass # Placeholder for actual call and detailed asserts
    ```

##### 1.1.2. Test Case: Ray Misses DSM - Points Away
*   **Description**: Test scenario where the ray points away from or parallel to the DSM surface and does not intersect.
*   **Inputs**: Similar to 1.1.1, but `d_world_normalized` points upwards (e.g., `[0, 0, 1]`) or parallel to the DSM plane (e.g., `[1, 0, 0]` if DSM is Z-plane).
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: Returns `(np.nan, np.nan, np.nan)`.
*   **Acceptance Criteria**: Correctly identifies no intersection by returning NaNs.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_misses_dsm_points_away(self):
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, 1.0]) # Points up, away from DSM at Z=10
        # mock_dsm_interpolator, mock_dsm_transform, mock_dsm_nodata_value, mock_config as in 1.1.1

        # Act
        # result = calculate_ray_dsm_intersection(P_sensor, d_world_normalized, mock_dsm_interpolator, mock_dsm_transform, mock_dsm_nodata_value, mock_config)

        # Assert
        # self.assertTrue(np.all(np.isnan(result)), "Expected (nan, nan, nan) for a missed ray.")
        pass # Placeholder
    ```

##### 1.1.3. Test Case: Ray Starts Below DSM and Points Upwards (Miss)
*   **Description**: Sensor is notionally below the DSM surface, ray points further away (upwards).
*   **Inputs**: `P_sensor` Z value is below DSM Z values (e.g., `P_sensor = [50,50,0]`, DSM at Z=10), `d_world_normalized` points upwards `[0,0,1]`.
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: `(np.nan, np.nan, np.nan)`.
*   **Acceptance Criteria**: Correctly identifies no intersection.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_starts_below_points_up(self):
        # Arrange
        P_sensor = np.array([50.0, 50.0, 0.0]) # Below DSM at Z=10
        d_world_normalized = np.array([0, 0, 1.0]) # Points up
        # mock_dsm_interpolator, mock_dsm_transform, mock_dsm_nodata_value, mock_config as in 1.1.1

        # Act
        # result = calculate_ray_dsm_intersection(...)

        # Assert
        # self.assertTrue(np.all(np.isnan(result)))
        pass # Placeholder
    ```

##### 1.1.4. Test Case: Ray Starts Inside DSM (Hypothetical)
*   **Description**: Sensor position is notionally within the DSM's vertical bounds (e.g., `P_sensor = [50,50,5]`, DSM is a thick slab from Z=0 to Z=10). This tests behavior if the initial point is "inside". The expected behavior is to find the intersection in the direction of the ray.
*   **Inputs**: `P_sensor` Z value is between min/max of a DSM feature. `d_world_normalized` points "out" (e.g., `[0,0,-1]`).
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: Finds the "exit" intersection point on the DSM surface.
*   **Acceptance Criteria**: Handles the scenario predictably, finding the correct surface intersection.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_starts_inside_dsm_points_out(self):
        # Arrange
        P_sensor = np.array([50.0, 50.0, 5.0]) # Inside a DSM from Z=0 to Z=10
        d_world_normalized = np.array([0, 0, -1.0]) # Points towards Z=0 surface
        # Mock DSM: e.g., a constant Z=0 surface for this test, or a more complex one.
        # For simplicity, assume DSM surface is at Z=0 for this ray direction.
        # x_coords = np.array([0, 100]); y_coords = np.array([0, 100])
        # z_values_surface0 = np.array([[0.0, 0.0], [0.0, 0.0]])
        # mock_dsm_interpolator_surface0 = RectBivariateSpline(x_coords, y_coords, z_values_surface0, kx=1, ky=1)
        # mock_dsm_transform, mock_dsm_nodata_value, mock_config
        # expected_X, expected_Y, expected_Z = 50.0, 50.0, 0.0

        # Act
        # result = calculate_ray_dsm_intersection(P_sensor, d_world_normalized, mock_dsm_interpolator_surface0, ...)

        # Assert
        # self.assertIsNotNone(result)
        # self.assertAlmostEqual(result[0], expected_X, places=5)
        # self.assertAlmostEqual(result[1], expected_Y, places=5)
        # self.assertAlmostEqual(result[2], expected_Z, places=5)
        pass # Placeholder
    ```

##### 1.1.5. Test Case: Ray Encounters Nodata Value - Initial Point
*   **Description**: The ray's projection at `t=0` (sensor XY) falls on a DSM cell marked as nodata.
*   **Inputs**: `dsm_interpolator` is mocked to return `dsm_nodata_value` (or NaN if that's how the interpolator signals it) for `(P_sensor[0], P_sensor[1])`.
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: `(np.nan, np.nan, np.nan)`.
*   **Acceptance Criteria**: Graceful handling of nodata at the start of the ray.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_nodata_at_sensor_xy(self):
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, -1.0])
        mock_dsm_nodata_value = -9999
        # Mock dsm_interpolator to return nodata_value at P_sensor's XY
        # class MockInterpolator:
        #     def __call__(self, x, y, grid=False):
        #         if np.allclose(x, P_sensor[0]) and np.allclose(y, P_sensor[1]):
        #             return np.array([[mock_dsm_nodata_value]]) if grid else mock_dsm_nodata_value
        #         return np.array([[10.0]]) # Some other valid Z
        # mock_dsm_interpolator = MockInterpolator()
        # mock_dsm_transform, mock_config

        # Act
        # result = calculate_ray_dsm_intersection(P_sensor, d_world_normalized, mock_dsm_interpolator, ...)

        # Assert
        # self.assertTrue(np.all(np.isnan(result)))
        pass # Placeholder
    ```

##### 1.1.6. Test Case: Ray Encounters Nodata Value - Along Path Before Intersection
*   **Description**: The ray passes through a nodata region before it would have intersected a valid DSM surface.
*   **Inputs**: `dsm_interpolator` returns nodata for points along the ray path that `brentq` might test, before a valid intersection.
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: `(np.nan, np.nan, np.nan)`.
*   **Acceptance Criteria**: Robustly handles nodata along the ray path if it prevents finding a valid intersection.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_nodata_along_path(self):
        # Arrange: P_sensor, d_world_normalized
        # Mock dsm_interpolator to return a valid Z at P_sensor,
        # then nodata in the middle of the ray path, then a valid Z further down.
        # The heuristic in func_to_solve might make brentq jump over small nodata areas.
        # This test needs careful mocking of dsm_interpolator to ensure brentq is affected by nodata.
        # mock_dsm_transform, mock_dsm_nodata_value, mock_config

        # Act
        # result = calculate_ray_dsm_intersection(...)

        # Assert
        # self.assertTrue(np.all(np.isnan(result))) # Assuming extensive nodata blocks intersection
        pass # Placeholder
    ```

##### 1.1.7. Test Case: Grazing Angle - Near Parallel
*   **Description**: Ray is almost parallel to a DSM surface feature.
*   **Inputs**: `d_world_normalized` results in a grazing angle with a mock DSM (e.g., a steep slope).
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: Either a successful (but potentially less precise) intersection or a failure (`np.nan`s) if `brentq` cannot converge or the ray "slips" past due to discrete steps.
*   **Acceptance Criteria**: Behaves predictably; if it fails for extreme grazing, it returns NaNs.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_grazing_angle(self):
        # Arrange
        P_sensor = np.array([0.0, 1.0, 10.0]) # Near a steep slope
        d_world_normalized = np.array([1.0, 0.0, -0.01]) # Almost horizontal, slightly down
        # Mock DSM: a steep slope, e.g., Z = X
        # x_coords = np.array([0, 100]); y_coords = np.array([0, 100])
        # z_values_slope = np.array([[x for x in x_coords] for _ in y_coords]]) # Z = X
        # mock_dsm_interpolator_slope = RectBivariateSpline(x_coords, y_coords, z_values_slope, kx=1, ky=1)
        # mock_dsm_transform, mock_dsm_nodata_value, mock_config

        # Act
        # result = calculate_ray_dsm_intersection(P_sensor, d_world_normalized, mock_dsm_interpolator_slope, ...)

        # Assert
        # This might result in NaNs or an imprecise hit. Define expected behavior.
        # For now, assume it might fail if too grazing.
        # if np.all(np.isnan(result)):
        #     pass # Expected failure for very grazing angle
        # else:
        #     # Check if result is somewhat reasonable if it hits
        #     self.assertAlmostEqual(result[2], result[0], delta=1.0) # Z approx X
        pass # Placeholder
    ```

##### 1.1.8. Test Case: `brentq` Fails to Converge (e.g., no sign change)
*   **Description**: Scenario where `func_to_solve` does not show a sign change within the search interval for `brentq`, causing `brentq` to raise a `ValueError`.
*   **Inputs**: Configure DSM and ray such that `z_ray - z_dsm` never changes sign (e.g., ray always above DSM and pointing away or parallel).
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: The function should catch the `ValueError` from `brentq` (if `brentq` is directly called and raises it) and return `(np.nan, np.nan, np.nan)`.
*   **Acceptance Criteria**: Gracefully handles `brentq` `ValueError` by returning NaNs.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_brentq_value_error_no_sign_change(self):
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([1.0, 0, 0]) # Parallel to DSM at Z=10
        # mock_dsm_interpolator (flat DSM at Z=10), mock_dsm_transform, mock_dsm_nodata_value, mock_config
        # This setup should ensure z_ray - z_dsm is always positive if ray stays above.

        # Act
        # result = calculate_ray_dsm_intersection(...)

        # Assert
        # self.assertTrue(np.all(np.isnan(result)), "Expected NaNs when brentq fails to find a root.")
        pass # Placeholder
    ```

##### 1.1.9. Test Case: Ray Exits DSM Bounding Box Horizontally
*   **Description**: The ray is directed such that it exits the horizontal bounds of the DSM (as defined by `dsm_transform` and interpolator's data) before an intersection is found.
*   **Inputs**: `dsm_interpolator` (via `get_dsm_z_safe`) will return nodata/NaN once outside defined XY bounds. Ray points towards outside.
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**: `(np.nan, np.nan, np.nan)`.
*   **Acceptance Criteria**: Correctly handles rays exiting DSM XY extent by returning NaNs.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_calculate_ray_dsm_intersection_exits_dsm_bounds_xy(self):
        # Arrange
        P_sensor = np.array([1.0, 1.0, 100.0]) # Inside a DSM defined from X=0-10, Y=0-10
        d_world_normalized = np.array([1.0, 0.0, -0.1]) # Points towards X positive, slightly down
        
        # DSM from X=0-10, Y=0-10, Z=0
        x_coords_small = np.array([0, 10])
        y_coords_small = np.array([0, 10])
        z_values_small = np.array([[0.0, 0.0], [0.0, 0.0]])
        mock_dsm_interpolator_small = RectBivariateSpline(x_coords_small, y_coords_small, z_values_small, kx=1, ky=1)
        # mock_dsm_transform (e.g., Affine.identity()), mock_dsm_nodata_value, mock_config
        # The ray should exit X=10 boundary.

        # Act
        # result = calculate_ray_dsm_intersection(P_sensor, d_world_normalized, mock_dsm_interpolator_small, ...)

        # Assert
        # self.assertTrue(np.all(np.isnan(result)))
        pass # Placeholder
    ```

### 1.2. Function: `run_georeferencing` (Non-Vectorized DSM Path)

Focus on the loop when `z_ground_method == "dsm_intersection"` and vectorization is not used or fails (covered further in fallback tests).

#### Test Cases for `run_georeferencing` (DSM Path)

##### 1.2.1. Test Case: Basic DSM Intersection for a Single Line (Iterative Mode)
*   **Description**: Test `run_georeferencing` for a single HSI line using DSM intersection in iterative (non-vectorized) mode.
*   **Inputs**:
    *   `hsi_data_struct`: Mocked HSI data for one line (e.g., a few pixels).
    *   `pose_data_df`: Mocked `pd.DataFrame` with pose data for that line.
    *   `sensor_model`: Mocked sensor model (vinkelx_rad, vinkely_rad).
    *   `dsm_interpolator`, `dsm_transform`, `dsm_nodata_value`: Mocked DSM components.
    *   `config`: `{"z_ground_method": "dsm_intersection", "georef_mode": "iterative", ...other_params}`.
    *   `lever_arm_m`: Mocked `np.array` for lever arm.
    *   `R_sensor_to_body`: Mocked `np.array` for rotation matrix.
*   **Actions**: Call `run_georeferencing`.
*   **Expected Outputs/Behavior**:
    *   Returns a `pd.DataFrame` of results, one row for each pixel, with `X_ground`, `Y_ground`, `Z_ground`.
    *   `calculate_ray_dsm_intersection` is called for each pixel.
    *   Results match pre-calculated expected values.
*   **Acceptance Criteria**: Correct georeferenced coordinates for all pixels in the line. `calculate_ray_dsm_intersection` is used.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    # from georeference_hsi_pixels import run_georeferencing
    # import pandas as pd

    # @patch('georeference_hsi_pixels.calculate_ray_dsm_intersection') # To verify calls and control output
    def test_run_georeferencing_dsm_intersection_single_line_iterative(self, mock_calc_intersect):
        # Arrange
        # Mock hsi_data_struct (e.g., num_samples=3, first_pixel_index=0)
        # Mock pose_data_df for one line
        # Mock sensor_model (e.g., simple angles for 3 pixels)
        # Mock dsm_components
        # config = {"z_ground_method": "dsm_intersection", "georef_mode": "iterative", ...}
        # Mock lever_arm_m, R_sensor_to_body
        
        # expected_pixel_results = [(x1,y1,z1), (x2,y2,z2), (x3,y3,z3)]
        # mock_calc_intersect.side_effect = expected_pixel_results
        # expected_df = pd.DataFrame(...) based on expected_pixel_results

        # Act
        # results_df = run_georeferencing(hsi_data_struct, pose_data_df, sensor_model, 
        #                                 dsm_interpolator, dsm_transform, dsm_nodata_value,
        #                                 config, lever_arm_m, R_sensor_to_body)

        # Assert
        # self.assertEqual(mock_calc_intersect.call_count, hsi_data_struct['num_samples'])
        # pd.testing.assert_frame_equal(results_df, expected_df, check_dtype=False, atol=1e-5)
        pass # Placeholder
    ```

### 1.3. Function: `run_georeferencing` (Integration Tests - Flat Plane vs. DSM)

These tests use minimal mocking of internal functions, focusing on the overall output with simple, verifiable inputs.

#### Test Cases for `run_georeferencing` (Integration)

##### 1.3.1. Test Case: Flat Plane (Vectorized) vs. Expected
*   **Description**: Integration test for `run_georeferencing` using flat-plane vectorized method.
*   **Inputs**:
    *   Small, well-defined HSI data (e.g., 1 line, 2 pixels).
    *   Corresponding pose data (e.g., sensor at known altitude, nadir pointing).
    *   Simple sensor model (e.g., pixel angles leading to known ground offsets).
    *   `config`: `{"z_ground_method": "flat_plane", "z_ground_plane_alt": 50.0, "georef_mode": "vectorized", ...}`.
    *   Lever arm (e.g., `[0,0,0]`), `R_sensor_to_body` (e.g., identity).
*   **Actions**: Call `run_georeferencing`.
*   **Expected Outputs/Behavior**: Output `pd.DataFrame` matches pre-calculated ground coordinates for flat-plane projection.
*   **Acceptance Criteria**: Results are accurate within tolerance (e.g., `1e-5`).
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_run_georeferencing_integration_flat_plane_vectorized_simple(self):
        # Arrange:
        # hsi_data_struct_mock = {'lines': 1, 'samples_per_line': 2, 'first_pixel_index': 0, ...}
        # pose_data_df_mock = pd.DataFrame({'timestamp': [...], 'X': [100], 'Y': [200], 'Z': [150], 
        #                                   'omega_deg': [0], 'phi_deg': [0], 'kappa_deg': [0]}) # Nadir
        # sensor_model_mock = {'vinkelx_rad': np.array([-0.1, 0.1]), 'vinkely_rad': np.array([0, 0])}
        # config_mock = {"z_ground_method": "flat_plane", "z_ground_plane_alt": 50.0, 
        #                "georef_mode": "vectorized", "max_workers": 1, ...} # Add other necessary config
        # lever_arm_m_mock = np.array([0.0, 0.0, 0.0])
        # R_sensor_to_body_mock = np.identity(3)
        # dsm_mocks = (None, None, None) # Not used for flat plane

        # Expected: Sensor at (100,200,150), ground at Z=50. Distance = 100.
        # Pixel 1: X = 100 + 100 * tan(-0.1) * cos(0) approx 100 - 10.01 = 89.98
        # Pixel 2: X = 100 + 100 * tan(0.1) * cos(0) approx 100 + 10.01 = 110.01
        # Y should be 200 for both. Z should be 50.
        # expected_df = pd.DataFrame({
        #     'hsi_line_index': [0, 0], 'pixel_index': [0, 1],
        #     'X_ground': [100 + 100 * np.tan(-0.1), 100 + 100 * np.tan(0.1)],
        #     'Y_ground': [200.0, 200.0], 'Z_ground': [50.0, 50.0]
        # })

        # Act
        # results_df = run_georeferencing(hsi_data_struct_mock, pose_data_df_mock, sensor_model_mock,
        #                                 *dsm_mocks, config_mock, lever_arm_m_mock, R_sensor_to_body_mock)
        # Assert
        # pd.testing.assert_frame_equal(results_df.reset_index(drop=True), expected_df.reset_index(drop=True), 
        #                               check_dtype=False, atol=1e-2) # Check precision
        pass # Placeholder
    ```

##### 1.3.2. Test Case: DSM Intersection (Iterative) vs. Expected
*   **Description**: Integration test for `run_georeferencing` using DSM intersection iterative method with a simple DSM.
*   **Inputs**: Similar to 1.3.1, but:
    *   Simple, well-defined DSM (e.g., a GeoTIFF of a small ramp: Z = X).
    *   `config`: `{"z_ground_method": "dsm_intersection", "georef_mode": "iterative", ...}`.
*   **Actions**: Call `run_georeferencing` (will need to load the test DSM).
*   **Expected Outputs/Behavior**: Output `pd.DataFrame` matches pre-calculated ground coordinates.
*   **Acceptance Criteria**: Results are accurate within a reasonable tolerance for DSM intersection (e.g., `1e-2`).
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    # Needs setup for creating/loading a test GeoTIFF DSM
    def test_run_georeferencing_integration_dsm_iterative_simple_ramp(self, tmp_path):
        # Arrange:
        # Create a simple ramp DSM (e.g., Z=X) as a GeoTIFF file in tmp_path
        # (Requires rasterio to write the test DSM)
        # dsm_path = tmp_path / "ramp_dsm.tif"
        # ... (code to create ramp_dsm.tif) ...
        
        # hsi_data_struct_mock, pose_data_df_mock (e.g., sensor looking straight down), sensor_model_mock
        # config_mock = {"z_ground_method": "dsm_intersection", "georef_mode": "iterative", 
        #                "dsm_path": str(dsm_path), "max_workers": 1, ...}
        # lever_arm_m_mock, R_sensor_to_body_mock

        # Pre-calculate expected_df based on sensor looking down at the ramp Z=X.
        # If sensor X=10, Y=10, Z_sensor=100, looking down, intersection should be X=10, Y=10, Z_dsm=10.

        # Act
        # results_df = run_georeferencing(hsi_data_struct_mock, pose_data_df_mock, sensor_model_mock,
        #                                 None, None, None, # DSM loaded internally from config
        #                                 config_mock, lever_arm_m_mock, R_sensor_to_body_mock)
        # Assert
        # pd.testing.assert_frame_equal(results_df, expected_df, check_dtype=False, atol=1e-2)
        pass # Placeholder
    ```

### 1.4. Fallback Mechanism: Vectorized to Non-Vectorized in `run_georeferencing`

(As per Prompt LS3_1, Req 5 and Prompt LS3_4, Req 4)

#### Test Cases for Fallback

##### 1.4.1. Test Case: Vectorized Processing Fails, Fallback to Iterative
*   **Description**: Test that if `process_hsi_line_vectorized` raises a specific exception (e.g., `VectorizedProcessingError`), `run_georeferencing` falls back to the non-vectorized (iterative) method for that line.
*   **Inputs**:
    *   Setup for `run_georeferencing` with `config` aiming for `georef_mode = "vectorized_then_iterative"` (or similar to enable fallback).
    *   `z_ground_method` can be "flat_plane" or "dsm_intersection".
    *   Mock `vectorized_georef.process_hsi_line_vectorized` to raise `VectorizedProcessingError`.
    *   Ensure components for the iterative fallback method are available (e.g., DSM interpolator if `z_ground_method` is "dsm_intersection").
*   **Actions**: Call `run_georeferencing`.
*   **Expected Outputs/Behavior**:
    *   `vectorized_georef.process_hsi_line_vectorized` is called and raises an exception.
    *   A warning is logged about the fallback.
    *   The appropriate iterative method (flat plane or DSM) is then called for pixels of that line.
    *   The final results for that line are based on the iterative method.
*   **Acceptance Criteria**: Fallback occurs, correct iterative method is used post-fallback, results are consistent with the iterative method, and appropriate logging occurs.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    from unittest.mock import patch
    # from pipeline_exceptions import VectorizedProcessingError # Assuming defined

    # @patch('georeference_hsi_pixels.process_hsi_line_iterative') # Or specific iterative function
    # @patch('georeference_hsi_pixels.vectorized_georef.process_hsi_line_vectorized')
    def test_run_georeferencing_fallback_from_vectorized_to_iterative(self, mock_process_vectorized, mock_process_iterative):
        # Arrange
        # mock_process_vectorized.side_effect = VectorizedProcessingError("Simulated vectorized error")
        # expected_iterative_results_for_line = pd.DataFrame(...) # What iterative processing should return
        # mock_process_iterative.return_value = expected_iterative_results_for_line
        
        # Mock hsi_data_struct, pose_data_df, sensor_model
        # config = {"georef_mode": "vectorized_then_iterative", "z_ground_method": "flat_plane", 
        #           "z_ground_plane_alt": 10.0, "max_workers": 1, ...} # Or dsm_intersection
        # Mock lever_arm_m, R_sensor_to_body
        # If dsm_intersection fallback, mock dsm_components.

        # Act
        # with self.assertLogs(level='WARNING') as cm: # Check for fallback warning
        #     results_df = run_georeferencing(...)
        # self.assertTrue(any("Falling back to individual pixel processing" in log_msg for log_msg in cm.output))

        # Assert
        # mock_process_vectorized.assert_called_once()
        # mock_process_iterative.assert_called_once() # Or check calls to calculate_ray_dsm_intersection etc.
        # Compare results_df for the affected line with expected_iterative_results_for_line
        pass # Placeholder
    ```

## 2. Module: `georeference_hsi_pixels.py` (Sensor Model Parsing)

### 2.1. Function: `parse_sensor_model` (Angle Interpretation - Prompt LS3_3)

#### Test Cases for `parse_sensor_model`

##### 2.1.1. Test Case: Input Angles are Degrees
*   **Description**: Test `parse_sensor_model` ([`georeference_hsi_pixels.py:149`](georeference_hsi_pixels.py:149)) when the input CSV file contains `vinkelx_deg` and `vinkely_deg` in degrees, and they are converted to radians.
*   **Inputs**:
    *   A temporary CSV file (e.g., `sensor_model_deg.txt`) with angle columns in degrees.
    *   `config` (potentially with `sensor_model_angle_units = "degrees"` if this option is added).
*   **Actions**: Call `parse_sensor_model(path_to_temp_file, config)`.
*   **Expected Outputs/Behavior**:
    *   The returned dictionary contains `vinkelx_rad` and `vinkely_rad` correctly converted to radians.
    *   Log message clearly states that input was degrees and conversion occurred.
*   **Acceptance Criteria**: Angles are correctly converted to radians.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_parse_sensor_model_angles_in_degrees(self, tmp_path):
        # Arrange
        sensor_model_content_deg = "pixel_index vinkelx_deg vinkely_deg\n0 90.0 45.0\n1 180.0 0.0"
        sensor_file = tmp_path / "sensor_model_deg.txt"
        sensor_file.write_text(sensor_model_content_deg)
        
        # Assuming the fix involves explicit conversion if names are _deg
        # Or, if a config option is added:
        # mock_config = {"sensor_model_angle_units": "degrees"}

        # Act
        # sensor_model = parse_sensor_model(str(sensor_file), config=mock_config if implemented else {})

        # Assert
        # self.assertAlmostEqual(sensor_model['vinkelx_rad'][0], np.deg2rad(90.0))
        # self.assertAlmostEqual(sensor_model['vinkely_rad'][0], np.deg2rad(45.0))
        # self.assertAlmostEqual(sensor_model['vinkelx_rad'][1], np.deg2rad(180.0))
        # Check logger output for "Converting degrees to radians" or similar.
        pass # Placeholder
    ```

##### 2.1.2. Test Case: Input Angles are Radians (Misnamed `_deg` in file)
*   **Description**: Test `parse_sensor_model` when the input CSV file columns are named `_deg` but actually contain radian values, and are correctly interpreted as radians.
*   **Inputs**:
    *   A temporary CSV file with angle columns in radians (but named `_deg`).
    *   `config` (potentially with `sensor_model_angle_units = "radians"`).
*   **Actions**: Call `parse_sensor_model`.
*   **Expected Outputs/Behavior**:
    *   The returned `vinkelx_rad` and `vinkely_rad` are used directly as radians.
    *   Log message clearly states that input was interpreted as radians.
*   **Acceptance Criteria**: Angles are correctly interpreted as radians without conversion.
*   **Python Scaffolding (in `test_georeferencing.py`)**:
    ```python
    def test_parse_sensor_model_angles_in_radians_misnamed(self, tmp_path):
        # Arrange
        rad_val_x, rad_val_y = np.pi / 2, np.pi / 4
        sensor_model_content_rad = f"pixel_index vinkelx_deg vinkely_deg\n0 {rad_val_x} {rad_val_y}"
        sensor_file = tmp_path / "sensor_model_rad_misnamed.txt"
        sensor_file.write_text(sensor_model_content_rad)
        
        # Assuming the fix involves reading names as _rad if config says so, or updating log
        # mock_config = {"sensor_model_angle_units": "radians"} # If implemented

        # Act
        # sensor_model = parse_sensor_model(str(sensor_file), config=mock_config if implemented else {})

        # Assert
        # self.assertAlmostEqual(sensor_model['vinkelx_rad'][0], rad_val_x)
        # self.assertAlmostEqual(sensor_model['vinkely_rad'][0], rad_val_y)
        # Check logger output for "Interpreting ... as RADIANS directly" or similar.
        pass # Placeholder
    ```

## 3. Module: `vectorized_georef.py` (Exception Handling - Prompt LS3_4)

Tests for `process_hsi_line_vectorized` ([`vectorized_georef.py:189`](vectorized_georef.py:189)) focusing on improved exception handling. These tests would typically reside in a `test_vectorized_georef.py` file.

### 3.1. Function: `process_hsi_line_vectorized`

#### Test Cases for `process_hsi_line_vectorized` (Exception Handling)

##### 3.1.1. Test Case: Invalid Quaternion Input
*   **Description**: Test behavior when `q_body_to_world_xyzw` is invalid for `Rotation.from_quat`.
*   **Inputs**:
    *   `q_body_to_world_xyzw`: An invalid quaternion (e.g., `[0,0,0,0]`, not normalized, wrong shape).
    *   Other valid inputs for the function (`P_imu_world`, `v_sensor_body_m_s`, `line_index`, `num_samples`, `sensor_model_line`, `R_sensor_to_body`, `effective_lever_arm_body`, `config`).
*   **Actions**: Call `process_hsi_line_vectorized`.
*   **Expected Outputs/Behavior**:
    *   Raises a specific custom exception (e.g., `VectorizedProcessingError` or `PoseTransformationError` from [`pipeline_exceptions.py`](pipeline_exceptions.py:1)).
    *   The exception should encapsulate details of the error.
*   **Acceptance Criteria**: Specific, informative exception is raised. Detailed error is logged.
*   **Python Scaffolding (e.g., in `test_vectorized_georef.py`)**:
    ```python
    # from vectorized_georef import process_hsi_line_vectorized
    # from pipeline_exceptions import VectorizedProcessingError, PoseTransformationError # Assuming these exist

    def test_process_hsi_line_vectorized_invalid_quaternion(self):
        # Arrange
        invalid_quat = np.array([0.0, 0.0, 0.0, 0.0]) # Not a unit quaternion
        # Mock other inputs: P_imu_world, v_sensor_body_m_s, etc.
        # mock_config = {'z_ground_method': 'flat_plane', 'z_ground_plane_alt': 0.0}
        # mock_sensor_model_line = {'vinkelx_rad': np.array([0.0]), 'vinkely_rad': np.array([0.0])}
        
        # Act & Assert
        # with self.assertRaises((VectorizedProcessingError, PoseTransformationError)) as cm:
        #     process_hsi_line_vectorized(P_imu_world_mock, invalid_quat, v_sensor_body_mock, 
        #                                 0, 1, mock_sensor_model_line, np.identity(3), 
        #                                 np.array([0,0,0]), mock_config)
        # self.assertIn("quaternion", str(cm.exception).lower()) # Check exception message content
        # Check logger for detailed error message (e.g., using self.assertLogs)
        pass # Placeholder
    ```

##### 3.1.2. Test Case: Matrix Calculation Error (Simulated)
*   **Description**: Test behavior when a matrix calculation internally fails (e.g., `np.linalg.LinAlgError`). This might be hard to trigger naturally with valid rotations, so mocking might be needed.
*   **Inputs**: Manipulate inputs or mock `scipy.spatial.transform.Rotation.as_matrix` to raise `LinAlgError`.
*   **Actions**: Call `process_hsi_line_vectorized`.
*   **Expected Outputs/Behavior**: Raises `VectorizedProcessingError` or `PoseTransformationError`.
*   **Acceptance Criteria**: Specific, informative exception is raised. Detailed error is logged.
*   **Python Scaffolding (e.g., in `test_vectorized_georef.py`)**:
    ```python
    # @patch('scipy.spatial.transform.Rotation.as_matrix')
    def test_process_hsi_line_vectorized_matrix_error(self, mock_as_matrix):
        # Arrange
        # mock_as_matrix.side_effect = np.linalg.LinAlgError("Simulated matrix error")
        # Valid q_body_to_world_xyzw, and other inputs
        # mock_config = {'z_ground_method': 'flat_plane', 'z_ground_plane_alt': 0.0}

        # Act & Assert
        # with self.assertRaises((VectorizedProcessingError, PoseTransformationError)):
        #     process_hsi_line_vectorized(...) # Call with valid inputs that would use the mocked method
        # Check logger for detailed error message
        pass # Placeholder
    ```

## 4. General Test Setup and Mocks

*   **Fixtures (Pytest)**: If using pytest, define fixtures for common mock objects (e.g., simple DSM interpolator, default config, basic sensor models, pose data DataFrames) to reduce boilerplate in test functions.
*   **Mocking Strategy**:
    *   **Unit Tests**: For functions like `calculate_ray_dsm_intersection`, mock external dependencies (e.g., `RectBivariateSpline`, file I/O if any) and complex internal calls if testing a specific part of the logic.
    *   **Integration Tests**: For `run_georeferencing`, use small, actual (but simple and verifiable) input data where possible (e.g., tiny CSVs for sensor model/poses, a small GeoTIFF for DSM). For components that are hard to set up with real files (like HSI data struct), use well-structured in-memory Python objects (dicts, DataFrames, NumPy arrays).
*   **Helper Functions for Test Data**: Create helper functions within the test suite to generate synthetic but valid HSI data structures, pose data DataFrames, and sensor model dictionaries for tests. This improves readability and maintainability of tests.
    ```python
    # Example test helper
    # def create_mock_pose_data(num_lines=1, **kwargs):
    #     base_pose = {'timestamp': 12345.0, 'X': 0, 'Y': 0, 'Z': 100, 'omega_deg': 0, 'phi_deg': 0, 'kappa_deg': 0}
    #     base_pose.update(kwargs)
    #     return pd.DataFrame([base_pose] * num_lines)
    ```
*   **Test File Structure**:
    *   Keep tests for [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) in [`test_georeferencing.py`](test_georeferencing.py:1).
    *   Tests for [`vectorized_georef.py`](vectorized_georef.py:1) should be in a corresponding `test_vectorized_georef.py` (create if it doesn't exist).

## 5. Acceptance Criteria for LS3 Testing Improvements

1.  **Coverage Increase**: Test coverage for [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) significantly increases, aiming for a target of 70-80%.
2.  **DSM Intersection Robustness**: All defined unit test cases for `calculate_ray_dsm_intersection` (and its potential refactored components) pass, demonstrating robust handling of diverse geometric and data scenarios (hits, misses, nodata, edge cases).
3.  **Sensor Angle Parsing**: `parse_sensor_model` correctly interprets and converts (if necessary) sensor angles from input files, as verified by its test cases.
4.  **Specific Exception Handling**:
    *   `vectorized_georef.process_hsi_line_vectorized` raises specific, informative custom exceptions (e.g., `VectorizedProcessingError`, `PoseTransformationError`) for identified error conditions like invalid quaternions or matrix math failures.
    *   `run_georeferencing` correctly catches these specific exceptions to trigger fallback mechanisms.
5.  **Integration Test Success**: Integration tests for `run_georeferencing` (covering both flat-plane vectorized and DSM iterative methods with simple, verifiable inputs) pass, with outputs matching pre-calculated expected values within acceptable tolerances.
6.  **Fallback Mechanism Verification**: The fallback from vectorized to iterative processing in `run_georeferencing` is successfully tested, ensuring it's triggered correctly by specific exceptions and that the iterative method produces the expected output for the affected data. Logging of fallback events is verified.
7.  **Test Quality**: All new test code is clear, well-documented, follows the Arrange-Act-Assert (AAA) pattern, and uses appropriate mocking or test data generation techniques. Tests are independent and repeatable.