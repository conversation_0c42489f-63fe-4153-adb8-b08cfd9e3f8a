# Prompts for Layer LS6: HSI Georeferencing Pipeline

## Overall Context for LS6

Layer LS5 revealed a critical bug in the vectorized processing path ([`reflection_LS5.md:29-37`](reflection_LS5.md:29-37)), which significantly impacted `correctness` (75.0) and `performance` (59.0) scores as shown in [`scores_LS5.json`](scores_LS5.json). Test coverage for [`vectorized_georef.py`](vectorized_georef.py:1) remains very low at 14% ([`scores_LS5.json:43`](scores_LS5.json:43)). The primary goal of LS6 is to rectify these critical issues and improve the robustness and efficiency of the vectorized operations.

---

## Prompt [LS6_1]

### Context
The most critical issue identified in [`reflection_LS5.md:29-37`](reflection_LS5.md:29-37) (Issue 1) is a bug in how `run_georeferencing` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) invokes `process_hsi_line_vectorized` from [`vectorized_georef.py`](vectorized_georef.py:1) for flat-plane calculations. This involves a mismatch in the `pose_data` structure and other parameters, as well as incorrect handling of the results. This bug effectively disables the vectorized path for flat-plane georeferencing, causing a fallback to per-pixel processing and severely impacting performance and correctness.

### Objective
Fix the critical bug in the invocation of `vectorized_georef.process_hsi_line_vectorized` from `run_georeferencing` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) and ensure correct handling of its results. This will enable the intended vectorized processing for flat-plane calculations.

### Focus Areas
-   Aligning the `pose_data` structure passed to `process_hsi_line_vectorized` with its expectations (raw pose components).
-   Correctly passing `z_ground_flat_plane` and `z_ground_method` parameters.
-   Modifying the result handling loop in `run_georeferencing` to correctly process the `List[Dict]` returned by `process_hsi_line_vectorized`.

### Code Reference
-   [`georeference_hsi_pixels.py:653-676`](georeference_hsi_pixels.py:653-676) (calling `process_hsi_line_vectorized` and handling results)
-   [`vectorized_georef.py:203-215`](vectorized_georef.py:203-215) (expected `pose_data` structure in `process_hsi_line_vectorized`)
-   [`reflection_LS5.md:38-63`](reflection_LS5.md:38-63) (problematic code snippets)
-   [`reflection_LS5.md:64-80`](reflection_LS5.md:64-80) (recommended fix details)

### Requirements
1.  **Modify `run_georeferencing` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) (around lines [`653-664`](georeference_hsi_pixels.py:653-664)):
    *   When calling `vectorized_georef.process_hsi_line_vectorized` for the flat-plane case:
        *   Pass the `current_pose` (which is a `pd.Series` or `Dict` containing raw pose components like `pos_x`, `pos_y`, `pos_z`, `quat_w`, `quat_x`, `quat_y`, `quat_z`) as the `pose_data` argument. Do NOT pass the pre-calculated `P_imu_world` and `R_body_to_world`.
        *   Ensure the `z_ground` argument is passed as `z_ground_flat_plane=Z_ground_flat_plane` (or the appropriate variable holding this value).
        *   Pass `z_ground_method="flat_plane"` (or the string constant representing this method).
2.  **Modify `run_georeferencing` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) (around lines [`667-676`](georeference_hsi_pixels.py:667-676)):
    *   Adapt the loop that processes `line_results` to correctly iterate through the `List[Dict]` returned by `vectorized_georef.process_hsi_line_vectorized`.
    *   Each dictionary in the list represents a single pixel's georeferenced data.
    *   Append a new dictionary to the main `results` list for each pixel, structured as follows (or similar, ensuring all necessary keys are present):
        ```python
        # Example structure for each item in the main results list
        {
            'hsi_line_index': i, # or pixel_data_dict['hsi_line_index']
            'pixel_index': pixel_data_dict['pixel_index'],
            'X_ground': pixel_data_dict['X_ground'],
            'Y_ground': pixel_data_dict['Y_ground'],
            'Z_ground': pixel_data_dict['Z_ground']
            # Potentially other fields like 'is_valid', 'error_code' if returned
        }
        ```
    *   Correctly update `nan_intersection_count` based on the `X_ground` (or equivalent validity flag) from each `pixel_data_dict`.
3.  **Add Unit Tests in [`test_georeferencing.py`](test_georeferencing.py:1)**:
    *   Create new test cases specifically verifying the correct invocation and result processing of the vectorized flat-plane path.
    *   These tests should mock `vectorized_georef.process_hsi_line_vectorized` to:
        *   Assert that it receives `pose_data` with raw components (e.g., `pos_x`, `quat_x`).
        *   Assert that it receives `z_ground_flat_plane` and `z_ground_method="flat_plane"`.
        *   Return a sample `List[Dict]` structure.
    *   Then, assert that `run_georeferencing` correctly processes this list into its main `results` list.

### Expected Improvements
-   Resolution of Critical Issue 1 from [`reflection_LS5.md`](reflection_LS5.md:29).
-   The vectorized path for flat-plane georeferencing in `run_georeferencing` will be correctly invoked and its results properly handled.
-   Significant improvement in the `correctness` score related to vectorized operations.
-   Potential improvement in `performance` for flat-plane cases, as the vectorized code will now execute.
-   Increased confidence in the flat-plane georeferencing results.

---

## Prompt [LS6_2]

### Context
The [`vectorized_georef.py`](vectorized_georef.py:1) module, which is crucial for performance, has extremely low test coverage (14% as per [`scores_LS5.json:43`](scores_LS5.json:43)). Furthermore, the performance score (59.0) is below target, and the LS5 goal of implementing vectorized DSM intersection was not met ([`scores_LS5.json:32`](scores_LS5.json:32)). Addressing these requires both comprehensive testing and targeted performance optimizations within [`vectorized_georef.py`](vectorized_georef.py:1).

### Objective
Significantly improve test coverage for [`vectorized_georef.py`](vectorized_georef.py:1) to ensure its robustness and correctness. Concurrently, optimize the performance of existing vectorized functions (especially for flat-plane, once LS6_1 is complete) and, if feasible within this layer, begin implementation or optimization of vectorized DSM intersection.

### Focus Areas
-   Writing comprehensive unit tests for all functions in [`vectorized_georef.py`](vectorized_georef.py:1), covering various scenarios, inputs, and edge cases.
-   Identifying and implementing performance optimizations within `process_hsi_line_vectorized` and its helper functions.
-   Exploring and potentially implementing vectorized DSM intersection logic.

### Code Reference
-   [`vectorized_georef.py`](vectorized_georef.py:1) (entire module)
-   [`test_georeferencing.py`](test_georeferencing.py:1) (for adding new tests for `vectorized_georef`)

### Requirements
1.  **Increase Test Coverage for [`vectorized_georef.py`](vectorized_georef.py:1)**:
    *   Write new unit tests in [`test_georeferencing.py`](test_georeferencing.py:1) (or a new dedicated test file like `test_vectorized_georef.py` if preferred, ensuring it's picked up by the test runner).
    *   Target all functions within [`vectorized_georef.py`](vectorized_georef.py:1), including:
        *   `_prepare_rotation_matrices_vectorized`
        *   `_calculate_sensor_pixel_vectors_vectorized`
        *   `_transform_vectors_to_world_vectorized`
        *   `_intersect_rays_with_horizontal_plane_vectorized`
        *   `process_hsi_line_vectorized` (especially its internal logic paths for flat-plane and DSM, error handling).
    *   Tests should cover:
        *   Valid inputs and expected outputs.
        *   Edge cases (e.g., zero vectors, NaN inputs if applicable, specific rotation angles).
        *   Error conditions and exception handling (e.g., `PoseTransformationError`, `VectorizedProcessingError`).
    *   Aim for a line coverage of at least 70% for [`vectorized_georef.py`](vectorized_georef.py:1).
2.  **Performance Optimization of `process_hsi_line_vectorized` (Flat-Plane)**:
    *   After LS6_1 ensures the flat-plane path is correctly invoked, profile and identify bottlenecks within `process_hsi_line_vectorized` and its helper functions when `z_ground_method="flat_plane"`.
    *   Implement optimizations such as:
        *   Ensuring efficient NumPy array operations.
        *   Minimizing redundant calculations.
        *   Pre-allocating arrays where possible.
3.  **Vectorized DSM Intersection (Optional Stretch Goal / Initial Implementation)**:
    *   If time and complexity allow after addressing points 1 and 2:
        *   Begin implementing a vectorized version of DSM intersection within [`vectorized_georef.py`](vectorized_georef.py:1). This could involve adapting the logic from `calculate_ray_dsm_intersection` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) for batch processing.
        *   Focus on the core ray-marching and intersection checks using NumPy for parallel operations.
        *   If full implementation is too complex for LS6, aim for a foundational implementation of the ray setup and initial marching steps in a vectorized manner, with clear TODOs for completion.
    *   Add corresponding unit tests for any new vectorized DSM intersection logic.
4.  **Benchmarking**:
    *   Add simple benchmark tests (e.g., using `pytest-benchmark` or custom timing) for the `process_hsi_line_vectorized` function for both flat-plane and (if attempted) DSM scenarios to quantify performance improvements.

### Expected Improvements
-   Line coverage for [`vectorized_georef.py`](vectorized_georef.py:1) increased to >70%.
-   Improved `performance` score, particularly if vectorized operations show speedups.
-   Increased robustness and reliability of the vectorized processing module.
-   Clearer understanding of performance characteristics of vectorized functions.
-   Progress towards a fully vectorized DSM intersection capability.

---

## Prompt [LS6_3]

### Context
Issue 2 from [`reflection_LS5.md:82-85`](reflection_LS5.md:82-85) highlights that relative DSM paths specified in the configuration file are resolved against the current working directory (`os.getcwd()`) in [`georeference_hsi_pixels.py:465-467`](georeference_hsi_pixels.py:465-467). This can lead to `FileNotFoundError` if the pipeline is executed from a directory different from where the DSM file is located relative to the config file or a project root.

### Objective
Improve the robustness of DSM file path resolution by making it relative to the configuration file's location or a well-defined project root, rather than the current working directory.

### Focus Areas
-   Modifying the path resolution logic in `load_dsm_data` (or the function responsible for loading/reading the DSM path from config) within [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1).
-   Ensuring the `config_file_path` is available or can be inferred in the context where the DSM path is resolved.

### Code Reference
-   [`georeference_hsi_pixels.py:465-467`](georeference_hsi_pixels.py:465-467) (current DSM path resolution)
-   [`reflection_LS5.md:92-99`](reflection_LS5.md:92-99) (recommended fix)

### Requirements
1.  In [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), within the function responsible for loading the DSM (likely `load_dsm_data` or where `dsm_file_path_from_config` is used, around lines [`465-467`](georeference_hsi_pixels.py:465-467)):
    *   Assume that the path to the main configuration file (e.g., `config.toml`) is available as a variable (e.g., `config_path`). If it's not directly available, investigate how it can be passed down or accessed (e.g., from `main_pipeline.py` execution context).
    *   Modify the logic for resolving `dsm_file_path_from_config`:
        *   Get the directory of the `config_path` using `Path(config_path).parent`.
        *   If `dsm_file_path_from_config` is a relative path, join it with the `config_dir` and resolve it to an absolute path.
        *   Example:
            ```python
            from pathlib import Path
            # ... assume config_path is the path to the loaded TOML configuration file ...
            # ... and dsm_file_path_from_config is read from this config ...

            config_dir = Path(config_path).parent
            dsm_path_resolved = Path(dsm_file_path_from_config)
            if not dsm_path_resolved.is_absolute():
                dsm_path_resolved = (config_dir / dsm_path_resolved).resolve()
            
            # Use dsm_path_resolved for loading the DSM
            ```
2.  **Update Unit Tests in [`test_georeferencing.py`](test_georeferencing.py:1)**:
    *   Modify existing tests or add new ones that specifically test this path resolution logic.
    *   This might involve:
        *   Creating temporary config files in test-specific subdirectories.
        *   Placing dummy DSM files relative to these temporary config files.
        *   Mocking `Path.exists()` or the actual DSM loading function to verify that the correct, resolved path is being attempted.
        *   Testing with both absolute and relative DSM paths in the mock config.

### Expected Improvements
-   Resolution of Issue 2 from [`reflection_LS5.md`](reflection_LS5.md:82).
-   More robust and predictable loading of DSM files, regardless of the directory from which the pipeline is executed.
-   Reduced likelihood of `FileNotFoundError` related to DSM paths.
-   Improved overall system reliability.