# Test Specifications for Layer LS5: HSI Georeferencing Pipeline

## 1. Overall Goals for LS5 Testing

The primary testing goals for Layer LS5 are:
1.  **Verify Fixes for LS4 Issues**: Ensure all code style issues, minor bugs, and logical ambiguities identified in [`reflection_LS4.md`](reflection_LS4.md) and targeted by `Prompt [LS5_1]` and `Prompt [LS5_2]` are resolved and their correct behavior is validated.
2.  **Increase Test Coverage**: Significantly improve line and branch coverage for the entire pipeline, with a particular focus on the core modules:
    *   [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py)
    *   [`vectorized_georef.py`](vectorized_georef.py)
    This addresses `Prompt [LS5_3]` and aims to bring coverage closer to the 80% target.
3.  **Validate Performance Enhancements**: Ensure that any performance optimizations implemented as per `Prompt [LS5_4]` do not compromise correctness. If new vectorized logic is introduced, it must be thoroughly tested for accuracy against existing methods.
4.  **Ensure Robustness**: Test error handling, edge cases, and different operational modes more comprehensively.
5.  **Maintain Adherence to Standards**: Ensure continued adherence to coding style and documentation standards.

## 2. Test Cases for Prompt [LS5_1]: Style and Minor Bug Fixes

**Note on Non-Unit-Testable Style Fixes:**
*   **Issue 1 (Local Imports in [`vectorized_georef.py`](vectorized_georef.py))**: This is a code style fix. Verification is by code inspection and ensuring that tests for `process_hsi_line_vectorized` (which might raise these exceptions) still pass and correctly catch `PoseTransformationError` and `VectorizedProcessingError` when appropriate, assuming they are now imported at the top level.
*   **Issue 2 (Missing Module Docstring in [`main_pipeline.py`](main_pipeline.py))**: This is a documentation fix. Verification is by code inspection.

### 2.1. `parse_hsi_header` Lever Arm Parsing ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py))

This addresses `Prompt [LS5_1]`, Requirement 3, related to Issue 4 from [`reflection_LS4.md`](reflection_LS4.md).

#### Test Case ID: LS5_1_GHP_PHH_01
*   **Description**: Test `parse_hsi_header` when only 'OffsetBetweenMainAntennaAndTargetPoint' key is present for lever arm.
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_hsi_header`
*   **Inputs**:
    *   Mock HSI header content string with: `OffsetBetweenMainAntennaAndTargetPoint = 1.0, 2.0, 3.0`
    *   No 'lever arm' key.
*   **Actions**: Call `parse_hsi_header` with the mock header content.
*   **Expected Outputs/Behavior**:
    *   The function returns a dictionary.
    *   `header_data['lever_arm']` should be `np.array([1.0, 2.0, 3.0])`.
*   **Acceptance Criteria**: The function correctly parses and prioritizes 'OffsetBetweenMainAntennaAndTargetPoint'.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_hsi_header_lever_arm_offset_only(self):
        header_content = "OffsetBetweenMainAntennaAndTargetPoint = 1.0, 2.0, 3.0\n"
        # ... other necessary mock header lines ...
        header_data = parse_hsi_header(io.StringIO(header_content))
        expected_lever_arm = np.array([1.0, 2.0, 3.0])
        np.testing.assert_array_almost_equal(header_data['lever_arm'], expected_lever_arm)
    ```

#### Test Case ID: LS5_1_GHP_PHH_02
*   **Description**: Test `parse_hsi_header` when only 'lever arm' key is present.
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_hsi_header`
*   **Inputs**:
    *   Mock HSI header content string with: `lever arm = 4.0, 5.0, 6.0`
    *   No 'OffsetBetweenMainAntennaAndTargetPoint' key.
*   **Actions**: Call `parse_hsi_header` with the mock header content.
*   **Expected Outputs/Behavior**:
    *   `header_data['lever_arm']` should be `np.array([4.0, 5.0, 6.0])`.
*   **Acceptance Criteria**: The function correctly parses 'lever arm' when 'OffsetBetweenMainAntennaAndTargetPoint' is absent.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_hsi_header_lever_arm_key_only(self):
        header_content = "lever arm = 4.0, 5.0, 6.0\n"
        # ... other necessary mock header lines ...
        header_data = parse_hsi_header(io.StringIO(header_content))
        expected_lever_arm = np.array([4.0, 5.0, 6.0])
        np.testing.assert_array_almost_equal(header_data['lever_arm'], expected_lever_arm)
    ```

#### Test Case ID: LS5_1_GHP_PHH_03
*   **Description**: Test `parse_hsi_header` when both 'OffsetBetweenMainAntennaAndTargetPoint' and 'lever arm' keys are present with different values.
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_hsi_header`
*   **Inputs**:
    *   Mock HSI header content string with:
        `OffsetBetweenMainAntennaAndTargetPoint = 1.0, 2.0, 3.0`
        `lever arm = 4.0, 5.0, 6.0`
*   **Actions**: Call `parse_hsi_header` with the mock header content. Capture logs.
*   **Expected Outputs/Behavior**:
    *   `header_data['lever_arm']` should be `np.array([1.0, 2.0, 3.0])` (prioritizing 'OffsetBetweenMainAntennaAndTargetPoint').
    *   A warning should be logged indicating both keys were found and their values differ, and which one was used.
*   **Acceptance Criteria**: The function prioritizes 'OffsetBetweenMainAntennaAndTargetPoint', uses its value, and logs a warning if values differ.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_hsi_header_lever_arm_both_keys_different_values(self):
        header_content = (
            "OffsetBetweenMainAntennaAndTargetPoint = 1.0, 2.0, 3.0\n"
            "lever arm = 4.0, 5.0, 6.0\n"
            # ... other necessary mock header lines ...
        )
        with self.assertLogs(level='WARNING') as log_capture:
            header_data = parse_hsi_header(io.StringIO(header_content))
        
        expected_lever_arm = np.array([1.0, 2.0, 3.0])
        np.testing.assert_array_almost_equal(header_data['lever_arm'], expected_lever_arm)
        self.assertTrue(any("both 'OffsetBetweenMainAntennaAndTargetPoint' and 'lever arm' found" in message for message in log_capture.output))
        self.assertTrue(any("Using value from 'OffsetBetweenMainAntennaAndTargetPoint'" in message for message in log_capture.output))
    ```

#### Test Case ID: LS5_1_GHP_PHH_04
*   **Description**: Test `parse_hsi_header` when both 'OffsetBetweenMainAntennaAndTargetPoint' and 'lever arm' keys are present with the same values.
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_hsi_header`
*   **Inputs**:
    *   Mock HSI header content string with:
        `OffsetBetweenMainAntennaAndTargetPoint = 1.0, 2.0, 3.0`
        `lever arm = 1.0, 2.0, 3.0`
*   **Actions**: Call `parse_hsi_header` with the mock header content. Capture logs.
*   **Expected Outputs/Behavior**:
    *   `header_data['lever_arm']` should be `np.array([1.0, 2.0, 3.0])`.
    *   No warning about differing values should be logged (or a less severe log indicating both found but consistent).
*   **Acceptance Criteria**: The function correctly parses and uses the prioritized key, and handles consistent redundant information gracefully.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_hsi_header_lever_arm_both_keys_same_values(self):
        header_content = (
            "OffsetBetweenMainAntennaAndTargetPoint = 1.0, 2.0, 3.0\n"
            "lever arm = 1.0, 2.0, 3.0\n"
            # ... other necessary mock header lines ...
        )
        # Option 1: No warning expected if values are identical and one is chosen
        # Option 2: A specific informational log might be expected
        header_data = parse_hsi_header(io.StringIO(header_content))
        expected_lever_arm = np.array([1.0, 2.0, 3.0])
        np.testing.assert_array_almost_equal(header_data['lever_arm'], expected_lever_arm)
        # Potentially assert no specific warning about differing values
    ```

### 2.2. `parse_sensor_model` Angle Interpretation Logging ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py))

This addresses `Prompt [LS5_1]`, Requirement 4, related to Issue 5 from [`reflection_LS4.md`](reflection_LS4.md).

#### Test Case ID: LS5_1_GHP_PSM_01
*   **Description**: Test `parse_sensor_model` when angles are clearly in radians (all values < 2π).
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_sensor_model`
*   **Inputs**: Mock sensor model file content with `vinkelx` and `vinkely` values like `0.1, -0.05, 0.0`.
*   **Actions**: Call `parse_sensor_model` with the mock file. Capture logs.
*   **Expected Outputs/Behavior**:
    *   Angles are parsed correctly and not converted from degrees.
    *   No warning log about degree-to-radian conversion.
*   **Acceptance Criteria**: Correctly interprets radian values without undue warnings.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_sensor_model_angles_in_radians(self):
        mock_sensor_model_content = """
        # ... other sensor model data ...
        vinkelx = 0.1, -0.05, 0.0
        vinkely = -0.1, 0.05, 0.02
        # ... other sensor model data ...
        """
        # Assuming parse_sensor_model takes a file-like object or path
        # and returns a dictionary or object with 'vinkelx_rad', 'vinkely_rad'
        with patch('builtins.open', mock_open(read_data=mock_sensor_model_content)):
            sensor_model_data = parse_sensor_model("dummy_path.txt")
        
        expected_vinkelx = np.array([0.1, -0.05, 0.0])
        expected_vinkely = np.array([-0.1, 0.05, 0.02])
        np.testing.assert_array_almost_equal(sensor_model_data['vinkelx_rad'], expected_vinkelx)
        np.testing.assert_array_almost_equal(sensor_model_data['vinkely_rad'], expected_vinkely)
        # Assert no warning log related to angle conversion
    ```

#### Test Case ID: LS5_1_GHP_PSM_02
*   **Description**: Test `parse_sensor_model` when angles are clearly in degrees (some values > 2π).
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_sensor_model`
*   **Inputs**: Mock sensor model file content with `vinkelx` and `vinkely` values like `10.0, -5.0, 180.0`.
*   **Actions**: Call `parse_sensor_model` with the mock file. Capture logs.
*   **Expected Outputs/Behavior**:
    *   Angles are interpreted as degrees and converted to radians.
    *   A warning log is generated, including the `max_abs_vinkelx` and `max_abs_vinkely` values that triggered the conversion (e.g., 180.0).
*   **Acceptance Criteria**: Correctly interprets degree values, converts them, and logs the specified warning.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_sensor_model_angles_in_degrees(self):
        mock_sensor_model_content = """
        # ... other sensor model data ...
        vinkelx = 10.0, -5.0, 180.0 
        vinkely = -20.0, 90.0, 0.0
        # ... other sensor model data ...
        """
        with patch('builtins.open', mock_open(read_data=mock_sensor_model_content)):
            with self.assertLogs(level='WARNING') as log_capture:
                sensor_model_data = parse_sensor_model("dummy_path.txt")
        
        expected_vinkelx_rad = np.deg2rad(np.array([10.0, -5.0, 180.0]))
        expected_vinkely_rad = np.deg2rad(np.array([-20.0, 90.0, 0.0]))
        np.testing.assert_array_almost_equal(sensor_model_data['vinkelx_rad'], expected_vinkelx_rad)
        np.testing.assert_array_almost_equal(sensor_model_data['vinkely_rad'], expected_vinkely_rad)
        
        self.assertTrue(any("Detected angle values > 2π" in message for message in log_capture.output))
        self.assertTrue(any("max_abs_vinkelx=180.000" in message for message in log_capture.output)) # Check formatting
        self.assertTrue(any("max_abs_vinkely=90.000" in message for message in log_capture.output)) # Check formatting
    ```

#### Test Case ID: LS5_1_GHP_PSM_03
*   **Description**: Test `parse_sensor_model` with mixed small and one large angle (e.g., `vinkelx = 0.1, 7.0, 0.2`) to ensure the heuristic triggers degree interpretation and logs correctly.
*   **Affected Function(s)**: `georeference_hsi_pixels.parse_sensor_model`
*   **Inputs**: Mock sensor model file content with `vinkelx = 0.1, 7.0, 0.2` and `vinkely = 0.05, 0.1, 0.15`. (7.0 is > 2π ≈ 6.28)
*   **Actions**: Call `parse_sensor_model` with the mock file. Capture logs.
*   **Expected Outputs/Behavior**:
    *   All angles are interpreted as degrees and converted to radians.
    *   A warning log is generated, including `max_abs_vinkelx=7.000` and `max_abs_vinkely=0.150`.
*   **Acceptance Criteria**: The heuristic correctly identifies the need for degree conversion based on the outlier and logs accurately.
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_parse_sensor_model_mixed_angles_trigger_degrees(self):
        mock_sensor_model_content = """
        # ... other sensor model data ...
        vinkelx = 0.1, 7.0, 0.2
        vinkely = 0.05, 0.1, 0.15
        # ... other sensor model data ...
        """
        with patch('builtins.open', mock_open(read_data=mock_sensor_model_content)):
            with self.assertLogs(level='WARNING') as log_capture:
                sensor_model_data = parse_sensor_model("dummy_path.txt")
        
        expected_vinkelx_rad = np.deg2rad(np.array([0.1, 7.0, 0.2]))
        expected_vinkely_rad = np.deg2rad(np.array([0.05, 0.1, 0.15]))
        np.testing.assert_array_almost_equal(sensor_model_data['vinkelx_rad'], expected_vinkelx_rad)
        np.testing.assert_array_almost_equal(sensor_model_data['vinkely_rad'], expected_vinkely_rad)
        
        self.assertTrue(any("Detected angle values > 2π" in message for message in log_capture.output))
        self.assertTrue(any("max_abs_vinkelx=7.000" in message for message in log_capture.output))
        self.assertTrue(any("max_abs_vinkely=0.150" in message for message in log_capture.output))
    ```

## 3. Test Cases for Prompt [LS5_2]: `brentq` Error Handling in `calculate_ray_dsm_intersection` ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py))

This addresses `Prompt [LS5_2]`, Requirements 1-5, related to Issue 3 from [`reflection_LS4.md`](reflection_LS4.md).
These tests will likely require mocking `scipy.optimize.brentq` and/or the `func_to_solve` passed to it, or carefully crafting DSM and ray scenarios.

#### Test Case ID: LS5_2_GHP_CRDI_01
*   **Description**: Test `calculate_ray_dsm_intersection` when `brentq` is expected to raise a `ValueError` because `f(a)` and `f(b)` have the same sign and no root is bracketed.
*   **Affected Function(s)**: `georeference_hsi_pixels.calculate_ray_dsm_intersection`
*   **Inputs**:
    *   Mock DSM, ray origin, ray direction.
    *   Scenario where `func_to_solve(a)` and `func_to_solve(b)` will consistently have the same sign over several ray marching steps.
*   **Actions**: Call `calculate_ray_dsm_intersection`. Mock `brentq` to raise `ValueError("f(a) and f(b) must have opposite signs")` when called under these conditions.
*   **Expected Outputs/Behavior**:
    *   The function should handle the `ValueError` gracefully.
    *   Ray marching should continue to the next interval.
    *   If no intersection is found within `max_dist`, it should return `None` or raise a specific "no intersection" error as per its design.
*   **Acceptance Criteria**: `brentq` `ValueError` leads to continued ray marching, not a crash. Correctly reports no intersection if applicable. (Req 2, 5)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    @patch('scipy.optimize.brentq')
    def test_calculate_ray_dsm_intersection_brentq_value_error_same_sign(self, mock_brentq):
        # Configure mock_brentq to raise ValueError when f(a) and f(b) would have same sign
        # This might involve a side_effect function that checks the arguments to func_to_solve
        mock_brentq.side_effect = ValueError("f(a) and f(b) must have opposite signs")
        
        # Setup mock_dsm_data, ray_origin, ray_direction, sensor_params, etc.
        # such that ray marching will occur over several steps.
        # ...
        
        # Expect the function to complete, possibly returning None if no intersection
        # or to have logged the continuation of ray marching.
        result = calculate_ray_dsm_intersection(
            self.mock_dsm_data, self.ray_origin, self.ray_direction, 
            self.sensor_params, self.config_params['max_dist']
        )
        self.assertIsNone(result) # Assuming None for no intersection
        # Potentially check logs for continuation messages
    ```

#### Test Case ID: LS5_2_GHP_CRDI_02
*   **Description**: Test `calculate_ray_dsm_intersection` when `func_to_solve` (passed to `brentq`) returns `NaN` at one or both interval boundaries.
*   **Affected Function(s)**: `georeference_hsi_pixels.calculate_ray_dsm_intersection`
*   **Inputs**:
    *   Mock DSM, ray origin, ray direction.
    *   Scenario where `func_to_solve(a)` or `func_to_solve(b)` returns `np.nan`.
*   **Actions**: Call `calculate_ray_dsm_intersection`. This might involve mocking `get_dsm_height_at_point` to return `NaN` for certain coordinates.
*   **Expected Outputs/Behavior**:
    *   The function should handle this gracefully.
    *   Ray marching should continue to find a segment where `func_to_solve` yields valid numbers.
    *   If no valid segment/intersection is found, it should return `None` or raise.
*   **Acceptance Criteria**: `NaN` from `func_to_solve` leads to continued ray marching. (Req 4, 5)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    @patch('georeference_hsi_pixels.get_dsm_height_at_point') 
    def test_calculate_ray_dsm_intersection_func_to_solve_returns_nan(self, mock_get_dsm_height):
        # Configure mock_get_dsm_height to return np.nan for specific points
        # that would be evaluated by func_to_solve at interval boundaries.
        def get_dsm_height_side_effect(dsm_data, x, y, method):
            if some_condition_for_nan: # e.g., specific x, y
                return np.nan
            return 100.0 # nominal height
        mock_get_dsm_height.side_effect = get_dsm_height_side_effect
        
        # Setup mock_dsm_data, ray_origin, ray_direction, etc.
        # ...

        result = calculate_ray_dsm_intersection(
            self.mock_dsm_data, self.ray_origin, self.ray_direction, 
            self.sensor_params, self.config_params['max_dist']
        )
        self.assertIsNone(result) # Or assert a successful intersection if a later segment is valid
        # Check logs for continuation.
    ```

#### Test Case ID: LS5_2_GHP_CRDI_03
*   **Description**: Test `calculate_ray_dsm_intersection` for grazing incidence where `val_at_a * val_at_b > 0` but one endpoint is very close to zero.
*   **Affected Function(s)**: `georeference_hsi_pixels.calculate_ray_dsm_intersection`
*   **Inputs**:
    *   Mock DSM, ray origin, ray direction.
    *   Scenario where `func_to_solve(a)` is very close to 0 (e.g., `1e-9`) and `func_to_solve(b)` is also small and positive (e.g., `1e-8`).
*   **Actions**: Call `calculate_ray_dsm_intersection`. Capture logs.
*   **Expected Outputs/Behavior**:
    *   The condition `np.isclose(val_at_a, 0)` or `np.isclose(val_at_b, 0)` should be met.
    *   A log message indicating grazing incidence should be recorded.
    *   Ray marching should continue.
    *   If no other intersection is found, it should return `None` or raise.
*   **Acceptance Criteria**: Grazing incidence is correctly identified, logged, and ray marching continues. (Req 3, 5)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    # This test might be tricky to set up perfectly without deep diving into func_to_solve behavior.
    # It might involve mocking parts of func_to_solve or get_dsm_height_at_point.
    def test_calculate_ray_dsm_intersection_grazing_incidence(self):
        # Setup DSM and ray such that func_to_solve(a) and func_to_solve(b) are both small positive,
        # with one being very close to zero.
        # e.g., ray just skims the top of a DSM feature.
        # ...
        
        with self.assertLogs(level='INFO') as log_capture: # Or DEBUG, depending on log level for grazing
            result = calculate_ray_dsm_intersection(
                self.mock_dsm_data_grazing, self.ray_origin_grazing, 
                self.ray_direction_grazing, self.sensor_params, self.config_params['max_dist']
            )
        
        self.assertTrue(any("Grazing incidence detected" in message for message in log_capture.output))
        # Assert result (e.g., None if no other intersection, or a found intersection if marching continues successfully)
    ```

#### Test Case ID: LS5_2_GHP_CRDI_04
*   **Description**: Test successful intersection found after initial `brentq` issues (e.g., `ValueError` or `NaN`) were handled and ray marching continued.
*   **Affected Function(s)**: `georeference_hsi_pixels.calculate_ray_dsm_intersection`
*   **Inputs**:
    *   Mock DSM, ray origin, ray direction.
    *   Scenario where the first few ray marching intervals lead to `brentq` issues (e.g., `f(a), f(b)` same sign), but a later interval is valid and contains a root.
*   **Actions**: Call `calculate_ray_dsm_intersection`.
*   **Expected Outputs/Behavior**:
    *   The function should successfully find and return the intersection point from the later valid interval.
*   **Acceptance Criteria**: Ray marching correctly recovers from initial `brentq` problems to find a valid intersection. (Req 5)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    @patch('scipy.optimize.brentq')
    @patch('georeference_hsi_pixels.get_dsm_height_at_point') # Or mock func_to_solve behavior directly
    def test_calculate_ray_dsm_intersection_success_after_brentq_issues(self, mock_get_dsm_height, mock_brentq):
        # Configure mocks:
        # - mock_get_dsm_height to create scenarios where initial intervals for brentq are problematic
        #   (e.g., f(a), f(b) same sign, or NaN).
        # - Then, for a later interval, make it valid for brentq.
        # - mock_brentq to raise ValueError for initial calls, then return a valid root for a later call.
        
        call_count = 0
        def brentq_side_effect(func, a, b, args, xtol, rtol, maxiter):
            nonlocal call_count
            call_count += 1
            if call_count <= 2: # First two attempts fail
                raise ValueError("f(a) and f(b) must have opposite signs")
            # Third attempt succeeds
            # Simulate func(a) and func(b) having opposite signs for this call
            # and return a plausible intersection distance
            return (a + b) / 2 # Simplified example of a root
            
        mock_brentq.side_effect = brentq_side_effect
        
        # Setup DSM, ray, etc.
        # ...
        
        result_coord, result_distance = calculate_ray_dsm_intersection(
            self.mock_dsm_data, self.ray_origin, self.ray_direction, 
            self.sensor_params, self.config_params['max_dist']
        )
        self.assertIsNotNone(result_coord)
        self.assertGreater(result_distance, 0)
        # Further assertions on the coordinate and distance based on setup
    ```

## 4. Test Cases for Prompt [LS5_3]: Test Coverage Improvement

### 4.1. Module: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py)

#### 4.1.1. Function: `get_dsm_height_at_point`
(LS4 added some tests; these aim for more comprehensive coverage including edge cases and interpolation methods if applicable.)

##### Test Case ID: LS5_3_GHP_GDHAP_01
*   **Description**: Test `get_dsm_height_at_point` with a point clearly inside the DSM extent.
*   **Inputs**: Mock DSM data (e.g., `rasterio.MemoryFile`), coordinates (x, y) within DSM bounds, valid interpolation method.
*   **Actions**: Call `get_dsm_height_at_point`.
*   **Expected Outputs/Behavior**: Returns the correctly interpolated DSM height.
*   **Acceptance Criteria**: Accurate height retrieval for points within DSM. (Req 114)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_get_dsm_height_at_point_inside_dsm(self):
        # self.mock_dsm_dataset is a rasterio dataset mock
        height = get_dsm_height_at_point(self.mock_dsm_dataset, 
                                         self.dsm_center_x, self.dsm_center_y, 
                                         method='linear') # or other methods
        self.assertIsNotNone(height)
        self.assertAlmostEqual(height, self.expected_center_height, places=5)
    ```

##### Test Case ID: LS5_3_GHP_GDHAP_02
*   **Description**: Test `get_dsm_height_at_point` with a point outside the DSM extent.
*   **Inputs**: Mock DSM data, coordinates (x, y) outside DSM bounds.
*   **Actions**: Call `get_dsm_height_at_point`.
*   **Expected Outputs/Behavior**: Returns `np.nan` (or raises a specific error, depending on design).
*   **Acceptance Criteria**: Graceful handling of points outside DSM. (Req 114)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_get_dsm_height_at_point_outside_dsm(self):
        height = get_dsm_height_at_point(self.mock_dsm_dataset, 
                                         self.outside_dsm_x, self.outside_dsm_y)
        self.assertTrue(np.isnan(height)) # Assuming NaN for outside points
    ```

##### Test Case ID: LS5_3_GHP_GDHAP_03
*   **Description**: Test `get_dsm_height_at_point` with a point exactly on the edge/corner of the DSM.
*   **Inputs**: Mock DSM data, coordinates (x, y) on DSM edge.
*   **Actions**: Call `get_dsm_height_at_point`.
*   **Expected Outputs/Behavior**: Returns a valid height (behavior might depend on interpolation and how edges are handled by `rasterio.sample`).
*   **Acceptance Criteria**: Correct behavior for edge cases. (Req 114)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_get_dsm_height_at_point_on_edge_dsm(self):
        height = get_dsm_height_at_point(self.mock_dsm_dataset, 
                                         self.dsm_edge_x, self.dsm_edge_y)
        # Assertion depends on expected behavior for edge points (e.g., not NaN)
        self.assertFalse(np.isnan(height)) 
    ```

#### 4.1.2. Function: `create_ray_dsm_difference_function`
(LS4 added some tests; aim for diverse inputs.)

##### Test Case ID: LS5_3_GHP_CRDDF_01
*   **Description**: Test `create_ray_dsm_difference_function` with various ray origins, directions, and DSM data.
*   **Inputs**: Mock DSM data, sensor parameters, ray origin (e.g., above DSM, below DSM), ray direction (e.g., pointing down, up, horizontal).
*   **Actions**: Create the difference function. Evaluate it at several distances along the ray.
*   **Expected Outputs/Behavior**: The difference function returns plausible height differences (ray height - DSM height).
*   **Acceptance Criteria**: Difference function behaves correctly for diverse scenarios. (Req 115)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_create_ray_dsm_difference_function_various_scenarios(self):
        # Scenario 1: Ray pointing down from above DSM
        ray_origin1 = np.array([self.dsm_center_x, self.dsm_center_y, self.expected_center_height + 100])
        ray_direction1 = np.array([0, 0, -1])
        diff_func1 = create_ray_dsm_difference_function(self.mock_dsm_dataset, self.sensor_params_mock, ray_origin1, ray_direction1)
        self.assertAlmostEqual(diff_func1(0), 100) # At origin, diff is initial height diff
        self.assertLess(diff_func1(100), 1) # Should be close to zero at intersection
        
        # Add more scenarios (ray pointing up from below, horizontal ray, etc.)
    ```

#### 4.1.3. Function: `find_dsm_entry_point`
(LS4 added some tests; aim for boundary conditions.)

##### Test Case ID: LS5_3_GHP_FDEP_01
*   **Description**: Test `find_dsm_entry_point` where entry point is found quickly.
*   **Inputs**: Mock DSM, ray origin (e.g., just outside DSM pointing in), ray direction.
*   **Actions**: Call `find_dsm_entry_point`.
*   **Expected Outputs/Behavior**: Returns a valid entry distance and the values `(val_at_a, val_at_b)` bracketing the entry.
*   **Acceptance Criteria**: Correctly finds entry point near origin. (Req 115)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_find_dsm_entry_point_found_quickly(self):
        # Setup ray_origin just outside DSM, pointing towards it
        # ...
        entry_dist, val_a, val_b = find_dsm_entry_point(
            self.mock_dsm_dataset, self.sensor_params_mock, 
            self.ray_origin_near_dsm, self.ray_direction_into_dsm, 
            self.config_params['max_dist'], self.config_params['initial_step_size']
        )
        self.assertIsNotNone(entry_dist)
        self.assertLess(entry_dist, self.config_params['initial_step_size'] * 5) # Example assertion
        self.assertTrue(val_a * val_b <= 0) # Should bracket the root
    ```

##### Test Case ID: LS5_3_GHP_FDEP_02
*   **Description**: Test `find_dsm_entry_point` where no entry point is found within `max_dist`.
*   **Inputs**: Mock DSM, ray origin (e.g., far from DSM pointing away), ray direction.
*   **Actions**: Call `find_dsm_entry_point`.
*   **Expected Outputs/Behavior**: Returns `(None, None, None)`.
*   **Acceptance Criteria**: Correctly handles no entry point found. (Req 115)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_find_dsm_entry_point_not_found(self):
        # Setup ray_origin far from DSM, pointing away
        # ...
        entry_dist, val_a, val_b = find_dsm_entry_point(
            self.mock_dsm_dataset, self.sensor_params_mock, 
            self.ray_origin_far_dsm, self.ray_direction_away_dsm, 
            self.config_params['max_dist'], self.config_params['initial_step_size']
        )
        self.assertIsNone(entry_dist)
        self.assertIsNone(val_a)
        self.assertIsNone(val_b)
    ```

#### 4.1.4. Function: `georeference_pixel_to_dsm`

##### Test Case ID: LS5_3_GHP_GPTD_01
*   **Description**: Test `georeference_pixel_to_dsm` with valid inputs leading to a successful georeferencing.
*   **Inputs**: Pixel coordinates (col, row), mock HSI header, mock sensor model, mock pose data, mock DSM data, config.
*   **Actions**: Call `georeference_pixel_to_dsm`.
*   **Expected Outputs/Behavior**: Returns a valid 3D coordinate (X, Y, Z).
*   **Acceptance Criteria**: Correct georeferencing for a standard case. (Req 116)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_georeference_pixel_to_dsm_successful(self):
        # self.hsi_header_mock, self.sensor_model_mock, self.pose_data_mock, self.dsm_data_mock, self.config_mock
        # ... setup mocks ...
        col, row = 50, 50 # Example pixel
        coord = georeference_pixel_to_dsm(
            col, row, self.hsi_header_mock, self.sensor_model_mock, 
            self.pose_data_mock, self.dsm_data_mock, self.config_mock
        )
        self.assertIsNotNone(coord)
        self.assertEqual(len(coord), 3)
        # Add assertions for expected coordinate values based on mock setup
    ```

##### Test Case ID: LS5_3_GHP_GPTD_02
*   **Description**: Test `georeference_pixel_to_dsm` where the ray does not intersect the DSM.
*   **Inputs**: Pixel coordinates, mocks, DSM data, such that the ray misses the DSM.
*   **Actions**: Call `georeference_pixel_to_dsm`.
*   **Expected Outputs/Behavior**: Returns `None` or (NaN, NaN, NaN).
*   **Acceptance Criteria**: Graceful handling of no DSM intersection. (Req 116)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_georeference_pixel_to_dsm_no_intersection(self):
        # Setup mocks such that the ray for the pixel (e.g., looking at sky) misses DSM
        # ...
        coord = georeference_pixel_to_dsm(
            self.pixel_col_sky, self.pixel_row_sky, self.hsi_header_mock, 
            self.sensor_model_mock, self.pose_data_sky_mock, 
            self.dsm_data_mock, self.config_mock
        )
        # Assuming None for no intersection, or check for NaNs
        self.assertTrue(coord is None or all(np.isnan(c) for c in coord))
    ```

#### 4.1.5. Function: `georeference_pixel_no_dsm`

##### Test Case ID: LS5_3_GHP_GPND_01
*   **Description**: Test `georeference_pixel_no_dsm` with valid inputs.
*   **Inputs**: Pixel coordinates (col, row), mock HSI header, mock sensor model, mock pose data, config (with `target_height_above_ground`).
*   **Actions**: Call `georeference_pixel_no_dsm`.
*   **Expected Outputs/Behavior**: Returns a valid 3D coordinate (X, Y, Z) where Z is based on `target_height_above_ground` relative to sensor height.
*   **Acceptance Criteria**: Correct georeferencing without DSM. (Req 116)
*   **Test Scaffolding (`test_georeferencing.py`):**
    ```python
    def test_georeference_pixel_no_dsm_successful(self):
        # ... setup mocks ...
        self.config_mock['georeferencing_options']['target_height_above_ground'] = 10.0 # meters
        col, row = 50, 50
        coord = georeference_pixel_no_dsm(
            col, row, self.hsi_header_mock, self.sensor_model_mock, 
            self.pose_data_mock, self.config_mock
        )
        self.assertIsNotNone(coord)
        self.assertEqual(len(coord), 3)
        # Expected Z = pose_z - target_height_above_ground (simplified, actual calculation depends on ray)
        # Add assertions for expected coordinate values
    ```

### 4.2. Module: [`vectorized_georef.py`](vectorized_georef.py)

(A new test file `test_vectorized_georef.py` might be needed or existing one expanded.)

#### 4.2.1. Function: `process_hsi_line_vectorized`

##### Test Case ID: LS5_3_VG_PHLV_01
*   **Description**: Test `process_hsi_line_vectorized` in no-DSM mode.
*   **Inputs**: Mock HSI line data (multiple pixels), pose data, sensor model, config (DSM disabled).
*   **Actions**: Call `process_hsi_line_vectorized`.
*   **Expected Outputs/Behavior**: Returns a list of georeferenced coordinates for all pixels in the line, calculated using the vectorized no-DSM path.
*   **Acceptance Criteria**: Correct vectorized processing for no-DSM scenario. (Req 119)
*   **Test Scaffolding (`test_vectorized_georef.py`):**
    ```python
    def test_process_hsi_line_vectorized_no_dsm(self):
        # self.hsi_line_data_mock (e.g., DataFrame or dict)
        # self.pose_for_line_mock, self.sensor_model_mock, self.config_no_dsm_mock
        # ...
        results_df = process_hsi_line_vectorized(
            0, # line_index
            self.hsi_line_data_mock, 
            self.pose_for_line_mock, 
            self.sensor_model_mock, 
            self.hsi_header_mock, 
            None, # dsm_data
            self.config_no_dsm_mock
        )
        self.assertIsNotNone(results_df)
        self.assertEqual(len(results_df), len(self.hsi_line_data_mock['pixels'])) # Or similar check
        # Add assertions on the calculated coordinates
    ```

##### Test Case ID: LS5_3_VG_PHLV_02
*   **Description**: Test `process_hsi_line_vectorized` in DSM mode where it falls back to per-pixel processing for DSM intersections.
*   **Inputs**: Mock HSI line data, pose data, sensor model, mock DSM data, config (DSM enabled).
    *   Ensure the setup triggers the fallback (e.g., if full vectorization of DSM is not yet implemented or for specific error conditions).
*   **Actions**: Call `process_hsi_line_vectorized`. Mock `georeference_pixel_to_dsm` to track calls.
*   **Expected Outputs/Behavior**:
    *   Returns a list of georeferenced coordinates.
    *   `georeference_pixel_to_dsm` (or its equivalent) is called for each pixel requiring DSM intersection.
    *   Results are consistent with per-pixel processing.
*   **Acceptance Criteria**: Fallback mechanism works correctly, and results are accurate. (Req 119)
*   **Test Scaffolding (`test_vectorized_georef.py`):**
    ```python
    @patch('vectorized_georef.georeference_pixel_to_dsm') # Or the actual path if imported differently
    def test_process_hsi_line_vectorized_dsm_fallback(self, mock_georef_pixel_to_dsm):
        # Configure mock_georef_pixel_to_dsm to return expected coordinates
        # ...
        # self.config_dsm_enabled_mock
        
        num_pixels = 5
        # Setup hsi_line_data_mock for num_pixels
        # ...

        mock_georef_pixel_to_dsm.return_value = (1.0, 2.0, 3.0) # Example return

        results_df = process_hsi_line_vectorized(
            0, self.hsi_line_data_mock, self.pose_for_line_mock, 
            self.sensor_model_mock, self.hsi_header_mock, 
            self.dsm_data_mock, self.config_dsm_enabled_mock
        )
        self.assertIsNotNone(results_df)
        self.assertEqual(mock_georef_pixel_to_dsm.call_count, num_pixels)
        # Assertions on results_df content
    ```

##### Test Case ID: LS5_3_VG_PHLV_03
*   **Description**: Test `process_hsi_line_vectorized` with invalid pose data that should trigger `PoseTransformationError` and be handled (e.g., result in NaNs for affected pixels or line).
*   **Inputs**: Mock HSI line data, invalid pose data (e.g., non-unit quaternion), sensor model, config.
*   **Actions**: Call `process_hsi_line_vectorized`.
*   **Expected Outputs/Behavior**:
    *   The function should not crash.
    *   It should produce results indicating failure for the line or specific pixels (e.g., DataFrame with NaNs, or an empty DataFrame with errors logged).
    *   `PoseTransformationError` should be caught internally if the design is to handle it per line.
*   **Acceptance Criteria**: Robust handling of pose transformation errors. (Req 110, 111)
*   **Test Scaffolding (`test_vectorized_georef.py`):**
    ```python
    def test_process_hsi_line_vectorized_invalid_pose(self):
        invalid_pose = self.pose_for_line_mock.copy()
        invalid_pose['q_body_to_world_xyzw'] = [1.0, 0.0, 0.0, 2.0] # Not a unit quaternion

        # This test assumes the function catches PoseTransformationError internally
        # and returns a DataFrame, possibly with NaNs or error flags.
        # If it's expected to re-raise, the test structure would change (e.g. self.assertRaises)
        
        results_df = process_hsi_line_vectorized(
            0, self.hsi_line_data_mock, invalid_pose, 
            self.sensor_model_mock, self.hsi_header_mock, 
            None, self.config_no_dsm_mock # Or with DSM
        )
        self.assertIsNotNone(results_df)
        # Example: Check if all X_utm, Y_utm, Z_height are NaN
        self.assertTrue(results_df['X_utm'].isnull().all()) 
    ```

## 5. Test Cases for Prompt [LS5_4]: Performance Improvements

Testing for performance improvements primarily involves:
1.  **Correctness**: Ensuring all existing functional tests (including those defined above) pass after any code optimizations.
2.  **Benchmarking**: (Outside the scope of these functional test specs, but crucial) Using tools like `timeit` or `cProfile` to measure performance gains.
3.  **Validation of New Vectorized Logic (if implemented)**: If DSM intersection is vectorized, new tests are needed to ensure its accuracy matches the per-pixel approach across various scenarios.

#### Test Case ID: LS5_4_VG_VDMI_01 (If Vectorized DSM Intersection is Implemented)
*   **Description**: Compare results of new vectorized DSM intersection logic with the previous per-pixel DSM intersection logic for a range of inputs.
*   **Affected Function(s)**: `vectorized_georef.process_hsi_line_vectorized` (new vectorized DSM path) vs. old path or `georeference_hsi_pixels.georeference_pixel_to_dsm`.
*   **Inputs**:
    *   Identical HSI line data, pose, sensor model, DSM data, config.
    *   Scenarios: simple flat DSM, complex terrain DSM, rays hitting different parts of DSM.
*   **Actions**:
    1.  Run georeferencing using the new vectorized DSM intersection path.
    2.  Run georeferencing using the old per-pixel DSM intersection path (or a trusted reference implementation).
*   **Expected Outputs/Behavior**: The 3D coordinates (X, Y, Z) obtained from both methods should be identical or very close within a defined tolerance.
*   **Acceptance Criteria**: New vectorized DSM logic is as accurate as the previous method. (Req 163, 165)
*   **Test Scaffolding (`test_vectorized_georef.py`):**
    ```python
    def test_vectorized_dsm_intersection_accuracy(self):
        # This test assumes a way to run both the new vectorized path and a reference path.
        # This might involve a flag in config or calling different internal functions.

        # Run with new vectorized DSM path
        results_vectorized_df = process_hsi_line_vectorized(
            0, self.hsi_line_data_complex_dsm_mock, self.pose_for_line_mock, 
            self.sensor_model_mock, self.hsi_header_mock, 
            self.dsm_data_complex_mock, self.config_vectorized_dsm_mock
        )

        # Run with reference per-pixel path (conceptual)
        # This might involve iterating and calling georeference_pixel_to_dsm
        reference_coords = []
        # for pixel_idx in range(len(self.hsi_line_data_complex_dsm_mock['pixels'])):
        #    # ... call georeference_pixel_to_dsm ...
        #    reference_coords.append(coord)
        # results_reference_df = pd.DataFrame(reference_coords, columns=['X_utm', 'Y_utm', 'Z_height']) # Example

        # For this example, let's assume we have a way to get reference results
        results_reference_df = self.get_reference_dsm_georef_results(
             self.hsi_line_data_complex_dsm_mock, self.pose_for_line_mock, 
            self.sensor_model_mock, self.hsi_header_mock, 
            self.dsm_data_complex_mock, self.config_per_pixel_dsm_mock
        )

        pd.testing.assert_frame_equal(
            results_vectorized_df[['X_utm', 'Y_utm', 'Z_height']],
            results_reference_df[['X_utm', 'Y_utm', 'Z_height']],
            check_dtype=False,
            atol=1e-3 # Define appropriate tolerance
        )
    ```

## Appendix: General Test Setup Considerations

*   **Mocking**:
    *   **DSM Data**: Use `rasterio.MemoryFile` to create in-memory mock DSMs with controlled elevation patterns (flat, sloped, complex).
    *   **HSI Header/Sensor Model**: Prepare mock Python dictionaries or use `io.StringIO` with text content to simulate file parsing.
    *   **Pose Data**: Create mock dictionaries or DataFrames for pose information.
    *   **Configuration**: Use mock dictionaries for `config` objects.
    *   **External Libraries**: `patch` from `unittest.mock` will be essential for mocking calls to libraries like `scipy.optimize.brentq` or file I/O.
*   **Fixtures**: For `pytest`, use fixtures to provide reusable mock data (DSMs, headers, sensor models, poses, configs). For `unittest`, use `setUp` methods.
*   **Data Precision**: Use `np.testing.assert_array_almost_equal` for comparing floating-point arrays (coordinates, lever arms) and `self.assertAlmostEqual` for scalar floats, with appropriate tolerances.
*   **Logging**: Use `self.assertLogs` context manager to capture and verify log messages for warnings and errors.
*   **Test Naming**: Follow descriptive naming conventions for test methods (e.g., `test_functionName_scenario_expectedBehavior`).
*   **Coverage Analysis**: After implementing tests, run `coverage.py` to identify remaining gaps and ensure the new tests effectively cover the intended code paths.