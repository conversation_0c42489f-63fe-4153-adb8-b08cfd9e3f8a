## Prompt [LS3_1]

### Context
The [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) module is critical for the georeferencing pipeline but has a dangerously low test coverage of 4% (as noted in `reflection_LS2.md`, Issue 1). This module includes complex logic for both vectorized flat-plane georeferencing and non-vectorized DSM intersection, including the `calculate_ray_dsm_intersection` function. The current tests in [`test_georeferencing.py`](test_georeferencing.py:1) are insufficient, primarily covering helper functions and basic integration with mocks.

### Objective
Significantly increase the test coverage for [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) to ensure the reliability and correctness of its core functionalities, particularly the DSM intersection path and the integration of different processing strategies.

### Focus Areas
-   Comprehensive testing of the non-vectorized pixel processing loop within `run_georeferencing` when `z_ground_method == "dsm_intersection"`.
-   Thorough unit testing of the `calculate_ray_dsm_intersection` function ([`georeference_hsi_pixels.py:155`](georeference_hsi_pixels.py:155)).
-   Integration testing for `run_georeferencing` covering both flat-plane (vectorized) and DSM intersection (non-vectorized) methods.
-   Verification of the fallback mechanism from vectorized to non-vectorized processing.

### Code Reference
Relevant files: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), [`test_georeferencing.py`](test_georeferencing.py:1)

### Requirements
1.  Expand [`test_georeferencing.py`](test_georeferencing.py:1) with new test cases.
2.  Add unit tests for the non-vectorized pixel processing loop in `run_georeferencing` (specifically for the `z_ground_method == "dsm_intersection"` path).
3.  Develop specific unit tests for `calculate_ray_dsm_intersection` covering various DSM scenarios:
    *   Successful intersection.
    *   Ray missing the DSM.
    *   Ray starting inside/outside the DSM.
    *   Ray encountering nodata values at different stages.
    *   Grazing angles.
    *   Cases where `brentq` might fail.
4.  Create integration tests for `run_georeferencing` using small, well-defined input HSI data, sensor models, and DSMs (where applicable). These tests should:
    *   Verify outputs for both flat-plane (vectorized) and DSM intersection (non-vectorized) methods.
    *   Compare results against pre-calculated, expected ground coordinates.
5.  Ensure tests cover the fallback mechanism from vectorized processing to non-vectorized processing in `run_georeferencing` when `process_hsi_line_vectorized` raises an exception.
6.  Mock external dependencies like file I/O and heavy computations within `rasterio` or `scipy.interpolate.RectBivariateSpline` where appropriate for unit tests, focusing on the logic within `georeference_hsi_pixels.py`.

### Expected Improvements
-   Increase test coverage for [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) to a minimum of 70-80%.
-   Enhanced confidence in the accuracy and robustness of the georeferencing results for both DSM and flat-plane methods.
-   Better detection of regressions in future development.

## Prompt [LS3_2]

### Context
The `calculate_ray_dsm_intersection` function ([`georeference_hsi_pixels.py:155-287`](georeference_hsi_pixels.py:155-287)) is identified as highly complex and difficult to test (as noted in `reflection_LS2.md`, Issue 2). Its current structure, involving ray marching, DSM boundary handling, nodata value management, and `brentq` for root finding, contributes to a high cyclomatic complexity (estimated at 30 for the critical module in `scores_LS2.json`) and hinders maintainability.

### Objective
Refactor the `calculate_ray_dsm_intersection` function to reduce its complexity, improve its readability, and enhance its testability by breaking it into smaller, well-defined helper functions and refining its internal logic.

### Focus Areas
-   Decomposition of the main function into logical sub-units.
-   Robustness of the DSM value retrieval and handling of nodata.
-   Clarity and reliability of the root-finding process using `brentq`.
-   Management of ray marching logic and boundary conditions.

### Code Reference
Function: `calculate_ray_dsm_intersection` in [`georeference_hsi_pixels.py:155-287`](georeference_hsi_pixels.py:155-287)
Example snippet highlighting complexity:
```python
# georeference_hsi_pixels.py
    def func_to_solve(t): # Nested function
        P_intersect = P_sensor + t * d_world_normalized
        x_ray, y_ray, z_ray = P_intersect[0], P_intersect[1], P_intersect[2]
        z_dsm = get_dsm_z(x_ray, y_ray)
        if np.isnan(z_dsm):
            return z_ray - (P_sensor[2] - 10000) # Heuristic
        return z_ray - z_dsm

    # ... (ray marching and brentq logic) ...
```

### Requirements
1.  **Refactor for Clarity**: Break down `calculate_ray_dsm_intersection` into smaller, more manageable, and independently testable helper functions. Potential candidates for helper functions include:
    *   A function to safely get DSM Z-value at a given (X, Y) coordinate, handling out-of-bounds and nodata values gracefully (e.g., `get_dsm_z_safe`).
    *   A function encapsulating the `brentq` bracketing logic, including the definition of `func_to_solve` and handling of its potential NaN returns or `ValueError` from `brentq`.
    *   A function for the core ray marching step or iteration.
2.  **Improve Heuristics/Error Handling**:
    *   Re-evaluate the heuristic `z_ray - (P_sensor[2] - 10000)` used when `z_dsm` is NaN within `func_to_solve`. Explore more robust strategies to guide `brentq` or handle cases where the ray exits the DSM coverage or encounters extensive nodata areas.
    *   Ensure clear error propagation or defined return values (e.g., specific exception or status flags) when an intersection cannot be reliably found.
3.  **Simplify Logic**: If possible, simplify the ray marching or intersection logic by identifying and potentially removing or streamlining handling for conditions that are exceptionally rare or could be managed by pre-checks before calling the main intersection routine.
4.  **Docstrings**: Update and add comprehensive docstrings for the refactored `calculate_ray_dsm_intersection` and all new helper functions, detailing parameters, return values, and exceptions raised.
5.  **Unit Tests**: (Covered by Prompt LS3_1, but ensure refactored components are unit-tested).

### Expected Improvements
-   Reduced cyclomatic complexity of the `calculate_ray_dsm_intersection` logic.
-   Improved readability, maintainability, and understandability of the DSM intersection code.
-   Enhanced testability of individual components of the intersection algorithm.
-   More robust handling of edge cases and difficult DSM intersection scenarios.

## Prompt [LS3_3]

### Context
Issue 3 in `reflection_LS2.md` highlights persistent confusion regarding the interpretation of sensor model angles (`vinkelx_deg`, `vinkely_deg`) in `parse_sensor_model` ([`georeference_hsi_pixels.py:149-153`](georeference_hsi_pixels.py:149-153)). The code reads columns with `_deg` suffixes but a comment suggests they are interpreted directly as radians, leading to ambiguity about the true units in the input CSV file and the correctness of the subsequent calculations.

### Objective
Eliminate ambiguity in sensor model angle interpretation by definitively determining the input units, ensuring correct conversion if necessary, and updating code and comments for clarity.

### Focus Areas
-   Verification of actual angle units in the sensor model CSV file.
-   Consistent naming of variables and DataFrame columns post-parsing.
-   Clear documentation (comments, logs) of unit handling.

### Code Reference
Function: `parse_sensor_model` in [`georeference_hsi_pixels.py:149-153`](georeference_hsi_pixels.py:149-153)
Relevant snippet:
```python
# georeference_hsi_pixels.py
# in parse_sensor_model
    sensor_data = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None, skiprows=1,
                              names=['pixel_index', 'vinkelx_deg', 'vinkely_deg'])
    logger.info("Interpreting 'vinkelx_deg' and 'vinkely_deg' columns from sensor model as RADIANS directly")
    vinkelx_rad = sensor_data['vinkelx_deg'].values # Still uses '_deg' key
    vinkely_rad = sensor_data['vinkely_deg'].values # Still uses '_deg' key
```

### Requirements
1.  **Clarify True Input Units**: Investigate the source sensor model file (`sensor_model.txt` or similar, as specified in `config.toml`) to determine definitively whether the `vinkelx_deg` and `vinkely_deg` columns contain values in degrees or radians.
2.  **Update Parsing and Conversion Logic**:
    *   **If the file contains degrees**:
        *   Perform an explicit `np.deg2rad()` conversion on these columns after reading the CSV in `parse_sensor_model`.
        *   Rename the DataFrame columns internally to reflect that they now hold radians (e.g., `vinkelx_rad`, `vinkely_rad`) before assigning to `vinkelx_rad` and `vinkely_rad` variables.
    *   **If the file truly contains radians (and `_deg` is a misnomer in the file)**:
        *   Modify the `names` parameter in `pd.read_csv` to `['pixel_index', 'vinkelx_rad', 'vinkely_rad']` to accurately reflect the content.
        *   If feasible and appropriate, recommend correcting the column headers in the source sensor model file itself.
3.  **Remove Ambiguous Comment/Log**: Update the `logger.info` message and any related comments in `parse_sensor_model` to clearly state the expected input unit from the file and explicitly mention any conversion being performed.
4.  **Configuration Option (Optional but Recommended)**: Consider adding a configuration parameter in `config.toml` (e.g., `sensor_model_angle_units = "degrees" / "radians"`) to allow users to specify the angle units in their sensor model file, making the parsing more flexible.
5.  **Update Tests**: Ensure any tests for `parse_sensor_model` reflect the corrected unit handling.

### Expected Improvements
-   Correct and unambiguous interpretation of sensor model angles.
-   Increased accuracy of georeferencing calculations dependent on these angles.
-   Improved code clarity and maintainability regarding unit handling.
-   Reduced risk of errors due to misinterpretation of angle units.

## Prompt [LS3_4]

### Context
Issues 4 and 5 in `reflection_LS2.md` point to generic `Exception` handling in `process_hsi_line_vectorized` ([`vectorized_georef.py:189-203`](vectorized_georef.py:189-203)) and in the fallback logic within `run_georeferencing` ([`georeference_hsi_pixels.py:598-600`](georeference_hsi_pixels.py:598-600)). This makes it difficult to diagnose the root cause of failures in the vectorized path and understand why a fallback to slower, per-pixel processing might be occurring.

### Objective
Implement more specific and informative exception handling in the vectorized georeferencing path and its fallback mechanism to improve error diagnosis and the robustness of the processing pipeline.

### Focus Areas
-   Replacing generic `except Exception` clauses with specific, meaningful exception types.
-   Propagating errors from `process_hsi_line_vectorized` to `run_georeferencing` effectively.
-   Providing detailed logging for errors and fallbacks.

### Code Reference
Function: `process_hsi_line_vectorized` in [`vectorized_georef.py:189-203`](vectorized_georef.py:189-203)
```python
# vectorized_georef.py
# in process_hsi_line_vectorized
    try:
        # ... quaternion conversion and matrix calculations ...
    except Exception as e: # Generic exception
        logger.error(f"Error in quaternion conversion for line {line_index}: {q_body_to_world_xyzw}, Error: {e}")
        # ... returns NaNs ...
```
Function: `run_georeferencing` in [`georeference_hsi_pixels.py:598-600`](georeference_hsi_pixels.py:598-600) (fallback logic)
```python
# georeference_hsi_pixels.py
# in run_georeferencing
            try:
                line_results = process_hsi_line_vectorized(...)
                # ...
            except Exception as e: # Generic exception for fallback
                logger.warning(f"Vectorized processing failed for line {i}: {e}. Falling back to individual pixel processing")
```

### Requirements
1.  **Refine Exception Handling in `process_hsi_line_vectorized` ([`vectorized_georef.py`](vectorized_georef.py:1))**:
    *   Identify potential specific exceptions that can occur during quaternion conversion (e.g., `ValueError` from `scipy.spatial.transform.Rotation.from_quat` for invalid quaternions) or matrix calculations (e.g., `numpy.linalg.LinAlgError`).
    *   Replace the generic `except Exception as e:` block with specific `except` clauses for these identified exceptions.
    *   For errors that indicate a problem with processing the line via vectorization but are recoverable by falling back, consider defining and raising a custom exception (e.g., `VectorizedProcessingError` or `PoseTransformationError` from [`pipeline_exceptions.py`](pipeline_exceptions.py:1)). This custom exception should encapsulate relevant details about the error.
    *   Ensure that logged error messages are detailed, including the line index and problematic pose data.
2.  **Update Fallback Logic in `run_georeferencing` ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1))**:
    *   Modify the `try-except` block that calls `process_hsi_line_vectorized` to catch the specific custom exception(s) (e.g., `VectorizedProcessingError`) raised by `process_hsi_line_vectorized` when a fallback is appropriate.
    *   Retain a more general `except Exception` only if truly unexpected errors need to be caught and logged before fallback, but prioritize specific exceptions.
    *   When a fallback occurs due to a caught specific exception, log detailed information about the original error that triggered the fallback (e.g., by logging the caught custom exception itself, which should contain context).
3.  **Consider Fallback Monitoring**:
    *   (Optional, based on assessment of current stability) If fallbacks are suspected to be frequent, implement a counter within `run_georeferencing` to track the number of lines that fall back to individual pixel processing. Log a summary warning if this count exceeds a certain threshold or percentage of total lines.
4.  **Update Tests**: Ensure tests (as per Prompt LS3_1) cover scenarios where `process_hsi_line_vectorized` fails and the fallback to non-vectorized processing is correctly triggered and functions as expected.

### Expected Improvements
-   More precise error identification in the vectorized processing logic.
-   Clearer understanding of why and when the system falls back to non-vectorized processing.
-   Improved diagnosability and maintainability of the georeferencing pipeline.
-   Reduced likelihood of silent failures or performance degradation due to unhandled issues in the vectorized path.